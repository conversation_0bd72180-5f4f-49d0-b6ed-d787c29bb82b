{"PriceScheme": [{"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000003", "Test_Name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "Test_Amount": 4000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000009", "Test_Name": "24 hrs URINE PROTEIN CREATININE RATIO", "Test_Amount": 400.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000011", "Test_Name": "Protein  Urine 24Hr", "Test_Amount": 350.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000012", "Test_Name": "Microalbumin  Urine 24Hr", "Test_Amount": 500.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000015", "Test_Name": "Acetylcholine Receptor (AChR) Antibody", "Test_Amount": 3000.0, "Old_Amount": 2700.0, "Spl_Amount": 2700.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000016", "Test_Name": "ACID PHOSPHATASE -PROSTATIC", "Test_Amount": 300.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000018", "Test_Name": "ADA", "Test_Amount": 800.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000025", "Test_Name": "Albumin.", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000026", "Test_Name": "ALCOHOL IN SERUM (Ethanol)", "Test_Amount": 1000.0, "Old_Amount": 800.0, "Spl_Amount": 800.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000027", "Test_Name": "Alkaline phosphatase", "Test_Amount": 100.0, "Old_Amount": 60.0, "Spl_Amount": 60.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000028", "Test_Name": "AMMONIA", "Test_Amount": 600.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000029", "Test_Name": "<PERSON><PERSON>", "Test_Amount": 450.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000030", "Test_Name": "APOLIPOPROTEIN A1", "Test_Amount": 700.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000031", "Test_Name": "APOLIPOPROTEIN B", "Test_Amount": 700.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000033", "Test_Name": "AMYLASE  (Body Fluid)", "Test_Amount": 400.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000038", "Test_Name": "Bicarbonate.", "Test_Amount": 150.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000040", "Test_Name": "Calcium  Urine 24Hr", "Test_Amount": 200.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000041", "Test_Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Test_Amount": 900.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000043", "Test_Name": "Chloride  CSF", "Test_Amount": 250.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000044", "Test_Name": "Chloride  Urine 24 Hrs", "Test_Amount": 250.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000045", "Test_Name": "CHLORIDE - PLEURAL  FLUID", "Test_Amount": 200.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000048", "Test_Name": "CHLORIDE SYNOVIAL FLUID", "Test_Amount": 200.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000049", "Test_Name": "Cholinesterase", "Test_Amount": 700.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000050", "Test_Name": "Cholesterol  Total", "Test_Amount": 80.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000052", "Test_Name": "COMPLEMENT C3", "Test_Amount": 600.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000053", "Test_Name": "COMPLEMENT C4", "Test_Amount": 600.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000054", "Test_Name": "CPK", "Test_Amount": 450.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000057", "Test_Name": "Creatinine.", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000058", "Test_Name": "CREATININE CLEARENCE- 24 HR URINE", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000061", "Test_Name": "D-Dimer (Quantitative)", "Test_Amount": 800.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000072", "Test_Name": "Electrophoresis - HB", "Test_Amount": 1200.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000073", "Test_Name": "ELECTROPHORESIS OF CSF", "Test_Amount": 550.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000074", "Test_Name": "ELECTROPHORESIS- 24 HRS URINE", "Test_Amount": 1350.0, "Old_Amount": 850.0, "Spl_Amount": 850.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000075", "Test_Name": "Glucose  Fasting", "Test_Amount": 30.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000079", "Test_Name": "Gamma Glutamyl-Transferase (GGT)", "Test_Amount": 200.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000080", "Test_Name": "GCT (100 GMS)", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000081", "Test_Name": "Globulin.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000084", "Test_Name": "GCT (50 gms)", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000086", "Test_Name": "HbA1c", "Test_Amount": 400.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000087", "Test_Name": "Cholesterol  HDL", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000090", "Test_Name": "IRON", "Test_Amount": 350.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000092", "Test_Name": "Lactate  CSF", "Test_Amount": 1000.0, "Old_Amount": 750.0, "Spl_Amount": 750.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000093", "Test_Name": "Lactate", "Test_Amount": 1000.0, "Old_Amount": 750.0, "Spl_Amount": 750.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000094", "Test_Name": "Lactate Dehydrogenase (LDH)", "Test_Amount": 350.0, "Old_Amount": 225.0, "Spl_Amount": 225.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000095", "Test_Name": "LDH - CSF", "Test_Amount": 350.0, "Old_Amount": 225.0, "Spl_Amount": 225.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000096", "Test_Name": "Lactate Dehydrogenase (LDH)  Ascitic Fluid", "Test_Amount": 355.0, "Old_Amount": 225.0, "Spl_Amount": 225.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000097", "Test_Name": "LDH -Peritoneal Fluid", "Test_Amount": 350.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000098", "Test_Name": "Lactate Dehydrogenase (LDH)  Pleural fluid", "Test_Amount": 350.0, "Old_Amount": 225.0, "Spl_Amount": 225.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000100", "Test_Name": "Cholesterol  LDL", "Test_Amount": 200.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000101", "Test_Name": "Lipase", "Test_Amount": 600.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000103", "Test_Name": "LIPOPROTEIN (a)", "Test_Amount": 700.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000105", "Test_Name": "Lithium", "Test_Amount": 600.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000106", "Test_Name": "Magnesium", "Test_Amount": 350.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000107", "Test_Name": "Microalbumin", "Test_Amount": 500.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000120", "Test_Name": "PLEURAL FLUID AMYLASE", "Test_Amount": 450.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000121", "Test_Name": "Potassium  Urine", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000122", "Test_Name": "Potassium  Urine 24Hr", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000123", "Test_Name": "Glucose  Post-prandial", "Test_Amount": 30.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000125", "Test_Name": "Protein   Pericardial fluid", "Test_Amount": 200.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000126", "Test_Name": "Protein  Ascitic Fluid", "Test_Amount": 200.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000128", "Test_Name": "Protein  Body fluids", "Test_Amount": 200.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000130", "Test_Name": "Protein  Pleural Fluid", "Test_Amount": 200.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000136", "Test_Name": "Protein  CSF", "Test_Amount": 200.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000139", "Test_Name": "Protein  Peritoneal Fluid", "Test_Amount": 200.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000147", "Test_Name": "PLASMA ACETONE", "Test_Amount": 100.0, "Old_Amount": 60.0, "Spl_Amount": 60.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000148", "Test_Name": "Aspartate aminotransferase (AST/SGOT)", "Test_Amount": 100.0, "Old_Amount": 60.0, "Spl_Amount": 60.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000149", "Test_Name": "Alanine aminotransferase (ALT/SGPT)", "Test_Amount": 100.0, "Old_Amount": 60.0, "Spl_Amount": 60.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000150", "Test_Name": "Sodium  Urine 24Hr", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000151", "Test_Name": "Sodium  Urine", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000152", "Test_Name": "Glucose  Ascitic Fluid", "Test_Amount": 100.0, "Old_Amount": 80.0, "Spl_Amount": 80.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000158", "Test_Name": "Glucose  Pleural Fluid", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000162", "Test_Name": "Glucose  CSF", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000164", "Test_Name": "Glucose  Body fluids", "Test_Amount": 100.0, "Old_Amount": 80.0, "Spl_Amount": 80.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000166", "Test_Name": "Total Iron Binding Capacity", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000167", "Test_Name": "Total Protein.", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000168", "Test_Name": "Transferrin Saturation", "Test_Amount": 400.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000169", "Test_Name": "Triglycerides", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000170", "Test_Name": "Troponin I", "Test_Amount": 700.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000171", "Test_Name": "Troponin T", "Test_Amount": 800.0, "Old_Amount": 650.0, "Spl_Amount": 650.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000172", "Test_Name": "Urea  Urine 24Hr", "Test_Amount": 250.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000173", "Test_Name": "Urea  Urine", "Test_Amount": 200.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000174", "Test_Name": "UREA CLEARANCE 24 HOUR URINE", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000175", "Test_Name": "Uric Acid.", "Test_Amount": 100.0, "Old_Amount": 80.0, "Spl_Amount": 80.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000177", "Test_Name": "Microalbumin/Creatinine   Urine", "Test_Amount": 500.0, "Old_Amount": 260.0, "Spl_Amount": 260.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000179", "Test_Name": "Calcium -  Urine", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000180", "Test_Name": "URINE CALCIUM CREATININE RATIO", "Test_Amount": 500.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000182", "Test_Name": "Creatinine  Urine", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000187", "Test_Name": "Opiates (Morphine)", "Test_Amount": 800.0, "Old_Amount": 650.0, "Spl_Amount": 650.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000194", "Test_Name": "MUCOPOLYSACCHARIDES (Glycosaminoglycan (GAG))", "Test_Amount": 2700.0, "Old_Amount": 2200.0, "Spl_Amount": 2200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000195", "Test_Name": "Urine Organic Acid  Complete Panel", "Test_Amount": 4000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000198", "Test_Name": "URINE PHOSPHOROUS", "Test_Amount": 300.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000199", "Test_Name": "Protein  Urine", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000200", "Test_Name": "Protein Creatinine Ratio  Urine", "Test_Amount": 400.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000201", "Test_Name": "URINE URIC ACID", "Test_Amount": 250.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000202", "Test_Name": "URINE  ALKAPTANURIA (Homogentisic Acid)", "Test_Amount": 1000.0, "Old_Amount": 750.0, "Spl_Amount": 750.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000203", "Test_Name": "Cholesterol  VLDL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000204", "Test_Name": "VMA -24 H<PERSON> Urine", "Test_Amount": 4000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000205", "Test_Name": "Sodium.", "Test_Amount": 250.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000206", "Test_Name": "Potassium.", "Test_Amount": 250.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000207", "Test_Name": "Chloride.", "Test_Amount": 250.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000208", "Test_Name": "Calcium", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000209", "Test_Name": "Phosphorous", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000210", "Test_Name": "ACID PHOSPHATASE -TOTAL", "Test_Amount": 300.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000211", "Test_Name": "PROSTATIC ACID PHOSPHATASE", "Test_Amount": 300.0, "Old_Amount": 140.0, "Spl_Amount": 140.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000214", "Test_Name": "Urea", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000215", "Test_Name": "BUN", "Test_Amount": 100.0, "Old_Amount": 40.0, "Spl_Amount": 40.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000216", "Test_Name": "Bilirubin  Total", "Test_Amount": 150.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000217", "Test_Name": "Bilirubin  Direct", "Test_Amount": 150.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000219", "Test_Name": "ELECTROPHORESIS-PROTEIN", "Test_Amount": 400.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000221", "Test_Name": "Uric Acid  Urine 24Hr", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000225", "Test_Name": "Creatinine  Urine 24Hr", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000227", "Test_Name": "URINE ALCOHOL", "Test_Amount": 1100.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000228", "Test_Name": "17- KETOSTEROIDS 24 hr.Urine", "Test_Amount": 4000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000230", "Test_Name": "Chloride  Urine", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000231", "Test_Name": "Bilirubin  Indirect", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000235", "Test_Name": "Glucose  30 min", "Test_Amount": 20.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000236", "Test_Name": "Glucose  60 min", "Test_Amount": 20.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000237", "Test_Name": "Glucose  90 min", "Test_Amount": 20.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000238", "Test_Name": "Glucose  120 min", "Test_Amount": 20.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000239", "Test_Name": "Glucose  150 min", "Test_Amount": 20.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000240", "Test_Name": "Glucose  180 min", "Test_Amount": 20.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000241", "Test_Name": "Glucose  Random", "Test_Amount": 30.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000242", "Test_Name": "Cholesterol  LDL (Direct)", "Test_Amount": 200.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000243", "Test_Name": "SUCROSE LYSIS TEST(PNH SCREENING TEST)", "Test_Amount": 450.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000244", "Test_Name": "WATER ANALYSIS (CHEMISTRY)", "Test_Amount": 3000.0, "Old_Amount": 3000.0, "Spl_Amount": 3000.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000245", "Test_Name": "Amino leveulinic acid (ALA)", "Test_Amount": 1300.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000247", "Test_Name": "BENZODIAZAPINE", "Test_Amount": 600.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000248", "Test_Name": "CYSTATIN-C", "Test_Amount": 1500.0, "Old_Amount": 750.0, "Spl_Amount": 750.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000249", "Test_Name": "Metanephrine  24 Hrs", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000250", "Test_Name": "Aldolase", "Test_Amount": 900.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000251", "Test_Name": "URINE CHROMATOGRAPHY", "Test_Amount": 500.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000252", "Test_Name": "URINE-CALCIUM", "Test_Amount": 200.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000253", "Test_Name": "URICACID / CREATININE", "Test_Amount": 200.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000255", "Test_Name": "DRUGS OF ABUSE - 9 Drugs", "Test_Amount": 4300.0, "Old_Amount": 3800.0, "Spl_Amount": 3800.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000256", "Test_Name": "Glomerular Filtration Rate (eGFR)", "Test_Amount": 250.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000259", "Test_Name": "Uricacid  Synovial fluid", "Test_Amount": 350.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000260", "Test_Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CO)", "Test_Amount": 3100.0, "Old_Amount": 2600.0, "Spl_Amount": 2600.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000263", "Test_Name": "HAPTOGLOBIN", "Test_Amount": 1500.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000264", "Test_Name": "Nicotine Metabolite", "Test_Amount": 1900.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000265", "Test_Name": "INHIBIN B", "Test_Amount": 1700.0, "Old_Amount": 1400.0, "Spl_Amount": 1400.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000266", "Test_Name": "Glucose  Ascitic Fluid.", "Test_Amount": 100.0, "Old_Amount": 80.0, "Spl_Amount": 80.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000267", "Test_Name": "Glucose Synovial Fluid", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000268", "Test_Name": "Protein Synovial Fluid", "Test_Amount": 250.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000269", "Test_Name": "VMA (SPOT)", "Test_Amount": 2500.0, "Old_Amount": 2100.0, "Spl_Amount": 2100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000270", "Test_Name": "METANEPHRINE (SPOT)", "Test_Amount": 2000.0, "Old_Amount": 1800.0, "Spl_Amount": 1800.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000271", "Test_Name": "Bile acids - Total", "Test_Amount": 2000.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000274", "Test_Name": "Cyclosporin (C2)", "Test_Amount": 3100.0, "Old_Amount": 2600.0, "Spl_Amount": 2600.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000275", "Test_Name": "Cannabis (Marijuana).", "Test_Amount": 800.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000276", "Test_Name": "PhenylAlanine Screen", "Test_Amount": 1600.0, "Old_Amount": 1400.0, "Spl_Amount": 1400.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000279", "Test_Name": "Myoglobin  Urine", "Test_Amount": 700.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000281", "Test_Name": "Biotinidase", "Test_Amount": 3500.0, "Old_Amount": 3000.0, "Spl_Amount": 3000.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000282", "Test_Name": "Calculus Study", "Test_Amount": 650.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000283", "Test_Name": "Ketone (D3 Hydroxybutyrate)", "Test_Amount": 2500.0, "Old_Amount": 2400.0, "Spl_Amount": 2400.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000284", "Test_Name": "GCT (75 gms)", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000285", "Test_Name": "Chylomicron  Qualitative", "Test_Amount": 500.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000287", "Test_Name": "Troponin- T (high sensitive)  Quantitative", "Test_Amount": 1700.0, "Old_Amount": 650.0, "Spl_Amount": 650.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000289", "Test_Name": "Metanephrine-free  plasma", "Test_Amount": 4000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000290", "Test_Name": "Citrate  Urine 24 Hrs", "Test_Amount": 1000.0, "Old_Amount": 800.0, "Spl_Amount": 800.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000291", "Test_Name": "STONE ANALYSIS", "Test_Amount": 1000.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000294", "Test_Name": "Hemosiderin", "Test_Amount": 600.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000295", "Test_Name": "Angiotensin Converting enzyme (ACE)", "Test_Amount": 1200.0, "Old_Amount": 650.0, "Spl_Amount": 650.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000296", "Test_Name": "OSMOLALITY (SERUM)", "Test_Amount": 700.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000297", "Test_Name": "Albumin/Globulin", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000298", "Test_Name": "Cholesterol/HDL Ratio", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000356", "Test_Name": "G6PD LEVEL", "Test_Amount": 900.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000419", "Test_Name": "Blood Urea Nitrogen (BUN)", "Test_Amount": 80.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000570", "Test_Name": "Homocysteine", "Test_Amount": 1100.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000588", "Test_Name": "OSMOLALITY (Urine)", "Test_Amount": 500.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000940", "Test_Name": "Determination of  Arsenic", "Test_Amount": 3000.0, "Old_Amount": 2700.0, "Spl_Amount": 2700.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001103", "Test_Name": "CRP", "Test_Amount": 400.0, "Old_Amount": 175.0, "Spl_Amount": 175.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001134", "Test_Name": "hs- CRP", "Test_Amount": 600.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001300", "Test_Name": "ADA (Adenosine Deaminase)", "Test_Amount": 800.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001316", "Test_Name": "Magnesium  Urine", "Test_Amount": 700.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001321", "Test_Name": "Methyl Malonic Acid", "Test_Amount": 900.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001329", "Test_Name": "Anion gap", "Test_Amount": 420.0, "Old_Amount": 225.0, "Spl_Amount": 225.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001332", "Test_Name": "Nor-Metanephrine  Urine 24 Hrs", "Test_Amount": 2400.0, "Old_Amount": 1900.0, "Spl_Amount": 1900.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001333", "Test_Name": "<PERSON><PERSON>", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001355", "Test_Name": "Oligoclonal band CSF", "Test_Amount": 5000.0, "Old_Amount": 4000.0, "Spl_Amount": 4000.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001364", "Test_Name": "Bun/Creatinine", "Test_Amount": 1.0, "Old_Amount": 1.0, "Spl_Amount": 1.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001407", "Test_Name": "DRUGS OF ABUSE - 12 Drugs", "Test_Amount": 6400.0, "Old_Amount": 5900.0, "Spl_Amount": 5900.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001409", "Test_Name": "LDL-Direct/HDL  Ratio", "Test_Amount": 1.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001410", "Test_Name": "Non - HDL Cholesterol", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001411", "Test_Name": "Est. Glomerular Filtration Rate", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001412", "Test_Name": "Porphophyrins  Urine 24 Hrs", "Test_Amount": 5000.0, "Old_Amount": 4400.0, "Spl_Amount": 4400.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001413", "Test_Name": "LP-PLA2 (LIPOPROTEIN ASSOCIATED PHOSPHOLIPASE A2)", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001431", "Test_Name": "TPMT ENZYME ACTIVITY (Thiopurine Methyl Transferase)", "Test_Amount": 4500.0, "Old_Amount": 4000.0, "Spl_Amount": 4000.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001432", "Test_Name": "CLOZAPINE WITH NOR-CLOZAPINE", "Test_Amount": 4000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001434", "Test_Name": "Tricyclic Antidepressants (TCA)", "Test_Amount": 1150.0, "Old_Amount": 650.0, "Spl_Amount": 650.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001436", "Test_Name": "LDL/HDL Ratio", "Test_Amount": 1.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001501", "Test_Name": "CSF Index", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001510", "Test_Name": "DRUGS OF ABUSE - 5 Drugs", "Test_Amount": 1800.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001512", "Test_Name": "DRUGS OF ABUSE - 10 Drugs", "Test_Amount": 4000.0, "Old_Amount": 3800.0, "Spl_Amount": 3800.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001513", "Test_Name": "Toxic Elements - 22", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001522", "Test_Name": "PYRUVATE", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001523", "Test_Name": "IA2 –INSULIN", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001525", "Test_Name": "METHYLMALONIC ACID –QUANTITATIVE", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001533", "Test_Name": "HDL/LDL-Direct Ratio", "Test_Amount": 1.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001547", "Test_Name": "<PERSON><PERSON> (Cotinine) Metabolite  Card", "Test_Amount": 2000.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001574", "Test_Name": "EVEROLIMUS", "Test_Amount": 5400.0, "Old_Amount": 4900.0, "Spl_Amount": 4900.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001584", "Test_Name": "Troponin- I (high sensitive)  Quantitative", "Test_Amount": 1700.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001594", "Test_Name": "Amyloid A", "Test_Amount": 4150.0, "Old_Amount": 3650.0, "Spl_Amount": 3650.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001645", "Test_Name": "CK-MB", "Test_Amount": 600.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001663", "Test_Name": "Non-Invasive Prenatal Testing (NIPT)", "Test_Amount": 10000.0, "Old_Amount": 7000.0, "Spl_Amount": 7000.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001664", "Test_Name": "Glucose  Post-Lunch", "Test_Amount": 30.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001665", "Test_Name": "Glucose  Post-Dinner", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001666", "Test_Name": "Glucose  Pre Dinner", "Test_Amount": 30.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001674", "Test_Name": "Whole Blood Clotting Test (WBCT)", "Test_Amount": 100.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001675", "Test_Name": "Glucose  Pre-Lunch", "Test_Amount": 30.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001678", "Test_Name": "URINE METABOLIC SCREENING", "Test_Amount": 6100.0, "Old_Amount": 5300.0, "Spl_Amount": 5300.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001703", "Test_Name": "BUN - URINE", "Test_Amount": 200.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001730", "Test_Name": "<PERSON><PERSON> (Nicotine) Card", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001737", "Test_Name": "ANTI CELL ANTIBODY", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00001", "Test_Name": "Lipid Profile", "Test_Amount": 400.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00002", "Test_Name": "Liver Function test", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00006", "Test_Name": "ELECTROLYTES", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00008", "Test_Name": "BILIRUBIN PROFILE", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00022", "Test_Name": "THYRO 5", "Test_Amount": 600.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00025", "Test_Name": "Renal Function Profile", "Test_Amount": 400.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00026", "Test_Name": "Iron Profile", "Test_Amount": 1200.0, "Old_Amount": 950.0, "Spl_Amount": 950.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00027", "Test_Name": "Glucose Tolerance Test - Regular (75 gms)", "Test_Amount": 300.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00029", "Test_Name": "Glucose Tolerance Test  (100 gms)", "Test_Amount": 300.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00030", "Test_Name": "Glucose Tolerance Test (75 gms)", "Test_Amount": 300.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00035", "Test_Name": "Mini Health Checkup", "Test_Amount": 500.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00036", "Test_Name": "Basic Master Health Checkup", "Test_Amount": 1000.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00037", "Test_Name": "Regular Master Health Checkup - Male", "Test_Amount": 1500.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00038", "Test_Name": "Regular Master Health Checkup - Female", "Test_Amount": 1500.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00042", "Test_Name": "CIVIL ENGINEERS PACKAGE (1+1)", "Test_Amount": 750.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00044", "Test_Name": "KGK- HEALTH PACKAGE", "Test_Amount": 1000.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00045", "Test_Name": "SURGICAL PACKAGE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00046", "Test_Name": "RENAL FUNCTION TESTS", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00047", "Test_Name": "TDH 200 PACKAGE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00048", "Test_Name": "TDH- ADOPTION 1000 PACKAGE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00049", "Test_Name": "RMR- MASTER HEALTH CHECK UP", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00050", "Test_Name": "RMR-<PERSON><PERSON><PERSON><PERSON> HEALTH SCREENING", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00051", "Test_Name": "RMR- WEL<PERSON> WOMEN HEALTH PACKAGE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00052", "Test_Name": "RMR- DIABETIC HEALTH SCREENING", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00053", "Test_Name": "RMR- EXECUTIVE HEALTH CHECK UP-MALE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00054", "Test_Name": "RMR- EXECUTIVE HEALTH CHECK UPE-FEMALE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00055", "Test_Name": "RMR- SENIOR CITIZEN COMPLETE HEALTH CHECK UP-MALE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00056", "Test_Name": "RMR- SENIOR CITIZEN COMPLETE HEALTH CHECK UP-FEMALE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00057", "Test_Name": "RMR- SENIOR CITIZEN HEALTH SCREENING.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00058", "Test_Name": "CBI-MASTER HEALTH CHECK UP (<40)", "Test_Amount": 2500.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00059", "Test_Name": "CBI-MASTER HEALTH CHECK UP (40 +)", "Test_Amount": 5250.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00060", "Test_Name": "RMR-- TDH CORA TRUST (RMRH)", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00061", "Test_Name": "KGK- HEALTH PACKAGE-1", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00062", "Test_Name": "RMR-ALC CARAMEL CHURCH", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00063", "Test_Name": "DIABETIC PROFILE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00064", "Test_Name": "DE ADDICTION PACKAGE", "Test_Amount": 400.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00065", "Test_Name": "KGK-COMPREHENSIVE DIABETIC CHECKUP CAMP", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00066", "Test_Name": "KIDNEY PROTECTION PROFILE-SUMMER OFFER", "Test_Amount": 400.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00067", "Test_Name": "ANNIVERSARY PACKAGE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00069", "Test_Name": "INAUGURAL OFFER", "Test_Amount": 300.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00071", "Test_Name": "PRE PROCEDURE TEST FOR ADVANCED GEL PRP", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00072", "Test_Name": "PRE PROCEDURE TEST FOR ADVANCED GEL PRP  STEM X27  REGEN PRO9", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00073", "Test_Name": "DIWALI SPECIAL OFFER -DIABETIC PROFILE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00074", "Test_Name": "PRE PROCEDURE TEST FOR ADVANCED GEL PRP  STEM X27  REGEN PRO 9- FEMALE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00075", "Test_Name": "MAXI VISION CATARACT PROFILE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00076", "Test_Name": "FITNESS HEALTH PROFILE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00077", "Test_Name": "PRE PROCEDURE TEST FOR <PERSON><PERSON>CUTANEOUS FUE- TITANIUM HAIR TRANSPLANTATION", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00078", "Test_Name": "AROGYAM B PROFLE-THYROCARE", "Test_Amount": 2000.0, "Old_Amount": 1400.0, "Spl_Amount": 1400.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00079", "Test_Name": "AROGYAM C PROFLE-THYROCARE", "Test_Amount": 2400.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@BC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00080", "Test_Name": "FERTILITY PACKAGE- FEMALE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000145", "Test_Name": "SEMEN FRUCTOSE", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000301", "Test_Name": "URINE SUGAR (F)", "Test_Amount": 10.0, "Old_Amount": 10.0, "Spl_Amount": 10.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000302", "Test_Name": "URINE SUGAR (PP)", "Test_Amount": 10.0, "Old_Amount": 10.0, "Spl_Amount": 10.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000303", "Test_Name": "URINE SUGAR", "Test_Amount": 10.0, "Old_Amount": 10.0, "Spl_Amount": 10.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000304", "Test_Name": "URINE SUGAR (30 mins)", "Test_Amount": 10.0, "Old_Amount": 10.0, "Spl_Amount": 10.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000305", "Test_Name": "URINE SUGAR (60 mins)", "Test_Amount": 10.0, "Old_Amount": 10.0, "Spl_Amount": 10.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000306", "Test_Name": "URINE SUGAR (90 mins)", "Test_Amount": 10.0, "Old_Amount": 10.0, "Spl_Amount": 10.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000307", "Test_Name": "URINE SUGAR (120 mins)", "Test_Amount": 10.0, "Old_Amount": 10.0, "Spl_Amount": 10.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000308", "Test_Name": "URINE SUGAR (180 mins)", "Test_Amount": 10.0, "Old_Amount": 10.0, "Spl_Amount": 10.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000309", "Test_Name": "URINE SUGAR (150 mins)", "Test_Amount": 10.0, "Old_Amount": 10.0, "Spl_Amount": 10.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000311", "Test_Name": "Chyluria", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000313", "Test_Name": "Cell count (CSF)", "Test_Amount": 300.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000315", "Test_Name": "ACETONE (URINE)", "Test_Amount": 100.0, "Old_Amount": 25.0, "Spl_Amount": 25.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000316", "Test_Name": "BENCE JONES PROTEIN-URINE", "Test_Amount": 100.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000317", "Test_Name": "Cell Count (Body Fluids)", "Test_Amount": 200.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000318", "Test_Name": "FAT GLOBULES (STOOL)", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000319", "Test_Name": "Stool complete examination", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000320", "Test_Name": "OCCULT BLOOD.", "Test_Amount": 100.0, "Old_Amount": 120.0, "Spl_Amount": 120.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000321", "Test_Name": "PREGNANCY TEST-URINE (CARD)", "Test_Amount": 100.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000322", "Test_Name": "REDUCING SUBSTANCE.", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000323", "Test_Name": "SEMEN ANALYSIS", "Test_Amount": 1000.0, "Old_Amount": 800.0, "Spl_Amount": 800.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000324", "Test_Name": "Urine Complete Examination", "Test_Amount": 100.0, "Old_Amount": 80.0, "Spl_Amount": 80.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000325", "Test_Name": "URINE ALBUMIN", "Test_Amount": 30.0, "Old_Amount": 15.0, "Spl_Amount": 15.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000408", "Test_Name": "URINE ACETONE", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000413", "Test_Name": "URINE OCCULT BLOOD", "Test_Amount": 100.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000414", "Test_Name": "Urine Routine Analysis", "Test_Amount": 1.0, "Old_Amount": 40.0, "Spl_Amount": 40.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001521", "Test_Name": "Urine Chemical Analysis.", "Test_Amount": 1.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001611", "Test_Name": "Urine Dysmorphic RBC", "Test_Amount": 800.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001634", "Test_Name": "Bile Salt", "Test_Amount": 30.0, "Old_Amount": 30.0, "Spl_Amount": 30.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001635", "Test_Name": "Bile Pigment", "Test_Amount": 30.0, "Old_Amount": 30.0, "Spl_Amount": 30.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001636", "Test_Name": "Color & Transparency", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001646", "Test_Name": "SPERM MOTILITY ANALYSIS-SEMEN", "Test_Amount": 550.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001648", "Test_Name": "DNA FRAGMENTATION INDEX", "Test_Amount": 5000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001654", "Test_Name": "Stool Ova & Cysts", "Test_Amount": 150.0, "Old_Amount": 80.0, "Spl_Amount": 80.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001704", "Test_Name": "INTRAUTERINE INSEMINATION -SPERM WASH", "Test_Amount": 2500.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@CP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00039", "Test_Name": "Bile Salt & Bile Pigment", "Test_Amount": 50.0, "Old_Amount": 60.0, "Spl_Amount": 60.0}, {"Dept_Code": "@EC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001668", "Test_Name": "HOMA IR (INSULIN RESISTANCE)", "Test_Amount": 1000.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@EC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001676", "Test_Name": "KARYOTYPING- SINGLE", "Test_Amount": 5000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@EC", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001677", "Test_Name": "KARYOTYPING- COUPLES", "Test_Amount": 7500.0, "Old_Amount": 5000.0, "Spl_Amount": 5000.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@", "Test_Name": "&nbsp;", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000076", "Test_Name": "FDP", "Test_Amount": 1000.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000181", "Test_Name": "URINE CHYLURIA", "Test_Amount": 250.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000280", "Test_Name": "Haemoglobin  Urine", "Test_Amount": 300.0, "Old_Amount": 220.0, "Spl_Amount": 220.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000326", "Test_Name": "Lupus Anticoagulant (dRVVT)", "Test_Amount": 1500.0, "Old_Amount": 850.0, "Spl_Amount": 850.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000327", "Test_Name": "FIBRINOGEN", "Test_Amount": 700.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000328", "Test_Name": "PARTIAL THROMBOPLASTIN TIME (APTT)", "Test_Amount": 300.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000329", "Test_Name": "PROTHROMBIN TIME", "Test_Amount": 250.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000330", "Test_Name": "ABSOLUTE EOSINOPHIL COUNT", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000331", "Test_Name": "ABSOLUTE LYMPHOCYTE COUNT", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000332", "Test_Name": "ABSOLUTE NEUTROPHIL COUNT", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000333", "Test_Name": "Anti Platelet Antibody", "Test_Amount": 7500.0, "Old_Amount": 6800.0, "Spl_Amount": 6800.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000342", "Test_Name": "BLEEDING TIME", "Test_Amount": 20.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000347", "Test_Name": "CLOTTING TIME", "Test_Amount": 20.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000348", "Test_Name": "COLD AGGLUTININ", "Test_Amount": 600.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000350", "Test_Name": "DIFFERENTIAL COUNT-5 Part", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000351", "Test_Name": "DIRECT COOMBS TEST", "Test_Amount": 350.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000353", "Test_Name": "ESR", "Test_Amount": 80.0, "Old_Amount": 40.0, "Spl_Amount": 40.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000361", "Test_Name": "Haemoglobin", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000364", "Test_Name": "INDIRECT COOMBS TEST", "Test_Amount": 350.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000366", "Test_Name": "MALARIAL  PARASITE (SLIDE)", "Test_Amount": 300.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000368", "Test_Name": "Tuberculin skin (Mantoux) Test", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000369", "Test_Name": "MCH", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000370", "Test_Name": "MCHC", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000371", "Test_Name": "MCV", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000372", "Test_Name": "Meth-haemoglobin  Blood", "Test_Amount": 1000.0, "Old_Amount": 850.0, "Spl_Amount": 850.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000374", "Test_Name": "MICROFILARIA (MF)", "Test_Amount": 300.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000375", "Test_Name": "FILARIAL ANTIGEN", "Test_Amount": 700.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000385", "Test_Name": "PCV", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000387", "Test_Name": "PH ( BLOOD)", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000390", "Test_Name": "PORPHOBILINOGEN -QUANTITAITVE", "Test_Amount": 4000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000391", "Test_Name": "Platelet count", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000396", "Test_Name": "Red Blood Cell (RBC) Count", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000398", "Test_Name": "RETICULOCYTE COUNT", "Test_Amount": 200.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000400", "Test_Name": "RH ANTIBODY TITRE", "Test_Amount": 170.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000402", "Test_Name": "SICKLING TEST", "Test_Amount": 100.0, "Old_Amount": 75.0, "Spl_Amount": 75.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000405", "Test_Name": "SPECIFIC GRAVITY URINE", "Test_Amount": 50.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000407", "Test_Name": "Total WBC count", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000410", "Test_Name": "URINE FAT GLOBULIN", "Test_Amount": 100.0, "Old_Amount": 30.0, "Spl_Amount": 30.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000412", "Test_Name": "URINE MYOGLOBIN", "Test_Amount": 600.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000423", "Test_Name": "GIEMSA STAINING", "Test_Amount": 100.0, "Old_Amount": 40.0, "Spl_Amount": 40.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000424", "Test_Name": "MALARIAL  PARASITE (CARD)", "Test_Amount": 300.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000425", "Test_Name": "NASAL SMEAR FOR EOSINOPHILS", "Test_Amount": 100.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000426", "Test_Name": "MYOGLOBIN-SERUM", "Test_Amount": 1200.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000427", "Test_Name": "Malarial Parasite (QBC)", "Test_Amount": 400.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000428", "Test_Name": "BLOOD GROUP&RH-GEL METHOD", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000429", "Test_Name": "RVVT - Russell viper venom time", "Test_Amount": 700.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000430", "Test_Name": "MALARIAL ANTIGEN (Vivax & Falciparum)", "Test_Amount": 600.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000431", "Test_Name": "MICROFILARIA (MF)  by QBC", "Test_Amount": 400.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000432", "Test_Name": "RDW - CV", "Test_Amount": 100.0, "Old_Amount": 30.0, "Spl_Amount": 30.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000436", "Test_Name": "Thrombin Time", "Test_Amount": 650.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000442", "Test_Name": "PERIPHERAL BLOOD SMEAR", "Test_Amount": 400.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000677", "Test_Name": "vW (<PERSON>) Factor  Plasma", "Test_Amount": 8500.0, "Old_Amount": 7500.0, "Spl_Amount": 7500.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000922", "Test_Name": "Chromosome Analysis - Product of Conception", "Test_Amount": 6100.0, "Old_Amount": 5500.0, "Spl_Amount": 5500.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001265", "Test_Name": "PLATELET COUNT.", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001276", "Test_Name": "FILARIAL ANTIBODY", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001298", "Test_Name": "Peripheral Smear", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001348", "Test_Name": "Factor X Functional", "Test_Amount": 5000.0, "Old_Amount": 4500.0, "Spl_Amount": 4500.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001537", "Test_Name": "Immature Platelet Fraction (IPF)", "Test_Amount": 1000.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001599", "Test_Name": "DCP- Decarboxy Prothrombin PIVKA II", "Test_Amount": 3600.0, "Old_Amount": 3100.0, "Spl_Amount": 3100.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001637", "Test_Name": "RDW - SD", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001638", "Test_Name": "Differential Count...", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001639", "Test_Name": "HCT", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001640", "Test_Name": "MPV.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001641", "Test_Name": "PDW", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001642", "Test_Name": "PCT", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001643", "Test_Name": "P-LCR", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001679", "Test_Name": "Haemoglobin.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001680", "Test_Name": "Total WBCs count.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001681", "Test_Name": "Large Immature Cells.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001682", "Test_Name": "Large Immature Cells#.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001683", "Test_Name": "Abs.Neutrophils in #.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001684", "Test_Name": "Abs.Lymphocyte in #.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001685", "Test_Name": "Abs.Eosinophils in #.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001686", "Test_Name": "Abs.Monocyte in #.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001687", "Test_Name": "Abs.Basophils in #.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001688", "Test_Name": "Red Blood Cell (RBC) Count.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001689", "Test_Name": "HCT (P.C.V).", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001690", "Test_Name": "MCV.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001691", "Test_Name": "MCH.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001692", "Test_Name": "MCHC.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001693", "Test_Name": "RDW-CV.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001694", "Test_Name": "RDW-SD.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001695", "Test_Name": "Platelets Count.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001696", "Test_Name": "PDW-SD.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001697", "Test_Name": "PDW-CV.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001698", "Test_Name": "P-LCR.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001699", "Test_Name": "PCT.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001700", "Test_Name": "RED BLOOD CELL COUNT-5P", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001701", "Test_Name": "PLATELET COUNT-5P", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00003", "Test_Name": "Complete Blood count- 5P", "Test_Amount": 400.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00020", "Test_Name": "CBC+ESR", "Test_Amount": 380.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00040", "Test_Name": "Complete Blood Count-3P", "Test_Amount": 300.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00068", "Test_Name": "Complete Blood Count-3P-T", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00070", "Test_Name": "COMPLETE BLOOD COUNT-5 Part.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000345", "Test_Name": "BLOOD GROUPING & Rh", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@HE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000447", "Test_Name": "BONE  MARROW ASPIRATION.", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000449", "Test_Name": "MF ON QBC.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001299", "Test_Name": "PAP - SMEAR", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001301", "Test_Name": "PAP Smear- <PERSON>I<PERSON>UID BASED CYTOLOGY (LBC)", "Test_Amount": 1200.0, "Old_Amount": 650.0, "Spl_Amount": 650.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001352", "Test_Name": "Biopsy Small", "Test_Amount": 750.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001430", "Test_Name": "IMMUNOHISTOCHEMISTRY", "Test_Amount": 7000.0, "Old_Amount": 7000.0, "Spl_Amount": 7000.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001505", "Test_Name": "Her-2/Neu(C-Er-B2)(ERBB2)by FISH", "Test_Amount": 10000.0, "Old_Amount": 9500.0, "Spl_Amount": 9500.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001619", "Test_Name": "HER 2/ NEU  FISH", "Test_Amount": 15000.0, "Old_Amount": 12500.0, "Spl_Amount": 12500.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001647", "Test_Name": "FNAC", "Test_Amount": 1000.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001650", "Test_Name": "Biopsy Small-1", "Test_Amount": 750.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001651", "Test_Name": "Biopsy Medium", "Test_Amount": 900.0, "Old_Amount": 650.0, "Spl_Amount": 650.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001652", "Test_Name": "Biopsy Large", "Test_Amount": 1200.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@HP", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001736", "Test_Name": "CYTOLOGY", "Test_Amount": 0.0, "Old_Amount": 650.0, "Spl_Amount": 650.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000002", "Test_Name": "17 - HYDROXY PROGESTERONE", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000007", "Test_Name": "Catecholamines  Urine 24 Hrs", "Test_Amount": 5000.0, "Old_Amount": 3200.0, "Spl_Amount": 3200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000013", "Test_Name": "5-HIAA (Hydroxy Indole Acetic Acid)  Urine", "Test_Amount": 4000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000017", "Test_Name": "ACTH (Adreno Corticotropic Hormone)", "Test_Amount": 1200.0, "Old_Amount": 750.0, "Spl_Amount": 750.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000085", "Test_Name": "Protein Electrophoresis", "Test_Amount": 850.0, "Old_Amount": 650.0, "Spl_Amount": 650.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000501", "Test_Name": "VITAMIN D PROFILE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000505", "Test_Name": "Alpha Fetoprotein (AFP)", "Test_Amount": 950.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000507", "Test_Name": "ALDOSTERONE", "Test_Amount": 2000.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000508", "Test_Name": "ALPHA 1. ANTITRIPSIN", "Test_Amount": 1500.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000511", "Test_Name": "AMINOGRAM  SERUM", "Test_Amount": 1700.0, "Old_Amount": 1250.0, "Spl_Amount": 1250.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000512", "Test_Name": "ANDROSTENEDIONE (A4)", "Test_Amount": 900.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000514", "Test_Name": "Thyroid peroxidase Antibody (Anti-TPO/ATMA)", "Test_Amount": 900.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000516", "Test_Name": "ANTI THROMBIN III activity", "Test_Amount": 1500.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000519", "Test_Name": "ANTI TPO", "Test_Amount": 0.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000520", "Test_Name": "Thyroglobulin Antibody (Anti-Tg)", "Test_Amount": 800.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000522", "Test_Name": "BETA hCG", "Test_Amount": 600.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000523", "Test_Name": "C-peptide  Fasting", "Test_Amount": 1100.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000524", "Test_Name": "C-peptide  postprandial", "Test_Amount": 1100.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000525", "Test_Name": "CA 15.3", "Test_Amount": 1000.0, "Old_Amount": 800.0, "Spl_Amount": 800.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000526", "Test_Name": "CA 19 .9", "Test_Amount": 1000.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000527", "Test_Name": "CALCITONIN", "Test_Amount": 1800.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000528", "Test_Name": "CARBAMAZEPINE", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000530", "Test_Name": "Catecholamines", "Test_Amount": 6000.0, "Old_Amount": 5800.0, "Spl_Amount": 5800.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000531", "Test_Name": "CD19", "Test_Amount": 2500.0, "Old_Amount": 2200.0, "Spl_Amount": 2200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000532", "Test_Name": "CD4: CD8 RATIO", "Test_Amount": 1400.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000533", "Test_Name": "CEA (Carcinoembryonic Antigen)", "Test_Amount": 700.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000535", "Test_Name": "CHROMOSOME ANALYSIS", "Test_Amount": 2750.0, "Old_Amount": 2250.0, "Spl_Amount": 2250.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000536", "Test_Name": "COPPER", "Test_Amount": 400.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000537", "Test_Name": "Copper  24 Hrs Urine", "Test_Amount": 1500.0, "Old_Amount": 1350.0, "Spl_Amount": 1350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000538", "Test_Name": "Cortisol   Urine -24 Hrs", "Test_Amount": 1000.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000539", "Test_Name": "Cortisol  Total", "Test_Amount": 800.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000540", "Test_Name": "CORTISOL (AM)", "Test_Amount": 800.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000541", "Test_Name": "CRYOGLOBULIN SERUM", "Test_Amount": 300.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000546", "Test_Name": "CYSTICERCOSIS IgG", "Test_Amount": 2200.0, "Old_Amount": 1700.0, "Spl_Amount": 1700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000548", "Test_Name": "DHEA-DehydroepiAndrostenedione", "Test_Amount": 2800.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000549", "Test_Name": "Dehydroepiandrosterone Sulfate (DHEA-S)", "Test_Amount": 800.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000550", "Test_Name": "DIGOXIN", "Test_Amount": 700.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000553", "Test_Name": "ESTRADIOL", "Test_Amount": 700.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000554", "Test_Name": "ESTRIOL - E3", "Test_Amount": 800.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000555", "Test_Name": "FACTOR V LEIDEN  Mutant Analysis (Real Time PCR)", "Test_Amount": 4000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000556", "Test_Name": "FERRITIN", "Test_Amount": 700.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000557", "Test_Name": "FOLIC ACID", "Test_Amount": 900.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000559", "Test_Name": "IONISED CALCIUM", "Test_Amount": 300.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000560", "Test_Name": "FREE PSA", "Test_Amount": 1000.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000561", "Test_Name": "FREE T3", "Test_Amount": 200.0, "Old_Amount": 110.0, "Spl_Amount": 110.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000562", "Test_Name": "FREE T4", "Test_Amount": 200.0, "Old_Amount": 110.0, "Spl_Amount": 110.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000563", "Test_Name": "Free Testosterone", "Test_Amount": 800.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000565", "Test_Name": "FRUCTOSAMINE", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000566", "Test_Name": "FSH", "Test_Amount": 500.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000567", "Test_Name": "GAD-65 - IgG", "Test_Amount": 4500.0, "Old_Amount": 4300.0, "Spl_Amount": 4300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000568", "Test_Name": "HBDH", "Test_Amount": 1200.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000569", "Test_Name": "HLA - B 27", "Test_Amount": 1900.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000571", "Test_Name": "GROWTH HORMONE", "Test_Amount": 600.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000572", "Test_Name": "IgG", "Test_Amount": 900.0, "Old_Amount": 275.0, "Spl_Amount": 275.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000573", "Test_Name": "Immunoglobulin D", "Test_Amount": 3000.0, "Old_Amount": 2800.0, "Spl_Amount": 2800.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000574", "Test_Name": "IgA", "Test_Amount": 900.0, "Old_Amount": 275.0, "Spl_Amount": 275.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000576", "Test_Name": "IgM", "Test_Amount": 900.0, "Old_Amount": 275.0, "Spl_Amount": 275.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000578", "Test_Name": "INHIBIN - B", "Test_Amount": 1700.0, "Old_Amount": 1400.0, "Spl_Amount": 1400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000579", "Test_Name": "INSULIN (F)", "Test_Amount": 900.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000580", "Test_Name": "INSULIN (PP)", "Test_Amount": 900.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000582", "Test_Name": "INSULIN LIKE GROWTH  FACTOR (IGF-1)", "Test_Amount": 2200.0, "Old_Amount": 1900.0, "Spl_Amount": 1900.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000583", "Test_Name": "IGF BP3", "Test_Amount": 2500.0, "Old_Amount": 2200.0, "Spl_Amount": 2200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000584", "Test_Name": "LEAD", "Test_Amount": 1300.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000585", "Test_Name": "LH", "Test_Amount": 500.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000587", "Test_Name": "Nor-Adrenaline (Nor-epinephrine)", "Test_Amount": 4200.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000592", "Test_Name": "PARACETAMOL", "Test_Amount": 1200.0, "Old_Amount": 850.0, "Spl_Amount": 850.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000593", "Test_Name": "PHENOBARBITONE", "Test_Amount": 900.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000594", "Test_Name": "PHENYTOIN", "Test_Amount": 700.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000596", "Test_Name": "PROGESTERONE", "Test_Amount": 900.0, "Old_Amount": 470.0, "Spl_Amount": 470.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000597", "Test_Name": "PROLACTIN", "Test_Amount": 500.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000598", "Test_Name": "PROTEIN-C Antigen", "Test_Amount": 4500.0, "Old_Amount": 4000.0, "Spl_Amount": 4000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000599", "Test_Name": "PROTEIN-S Free Antigen", "Test_Amount": 5000.0, "Old_Amount": 4500.0, "Spl_Amount": 4500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000600", "Test_Name": "PSA", "Test_Amount": 600.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000601", "Test_Name": "Parathyroid Hormone  Intact (iPTH)", "Test_Amount": 1000.0, "Old_Amount": 650.0, "Spl_Amount": 650.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000609", "Test_Name": "SERUM METANEPHRINES", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000611", "Test_Name": "SEX HORMONE BINDING GL<PERSON><PERSON><PERSON>LIN", "Test_Amount": 600.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000613", "Test_Name": "URINARY STONE ANALYSIS", "Test_Amount": 1000.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000614", "Test_Name": "Tri-Iodothyronine Total (TT3)", "Test_Amount": 150.0, "Old_Amount": 80.0, "Spl_Amount": 80.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000615", "Test_Name": "Thyroxine Total (TT4)", "Test_Amount": 150.0, "Old_Amount": 80.0, "Spl_Amount": 80.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000616", "Test_Name": "Testosterone  Total", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000619", "Test_Name": "THYROGLOBULIN", "Test_Amount": 800.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000622", "Test_Name": "Transferrin", "Test_Amount": 1100.0, "Old_Amount": 750.0, "Spl_Amount": 750.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000623", "Test_Name": "Thyroid Stimulating Hormone (TSH)", "Test_Amount": 200.0, "Old_Amount": 80.0, "Spl_Amount": 80.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000626", "Test_Name": "VALPROIC ACID", "Test_Amount": 600.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000627", "Test_Name": "VALPROIC ACID (F)", "Test_Amount": 600.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000628", "Test_Name": "VITAMIN  A (Retinol)", "Test_Amount": 2500.0, "Old_Amount": 2300.0, "Spl_Amount": 2300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000629", "Test_Name": "VITAMIN B 12", "Test_Amount": 1200.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000630", "Test_Name": "25 Hydroxy Vitamin D3", "Test_Amount": 1200.0, "Old_Amount": 950.0, "Spl_Amount": 950.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000631", "Test_Name": "VITAMIN E (Tocopherol)", "Test_Amount": 2900.0, "Old_Amount": 2600.0, "Spl_Amount": 2600.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000632", "Test_Name": "ZINC", "Test_Amount": 450.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000633", "Test_Name": "Cancer Antigen 125 (CA-125)", "Test_Amount": 900.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000637", "Test_Name": "IgE", "Test_Amount": 900.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000638", "Test_Name": "Prenatal Screening - 2nd TRIMESTER", "Test_Amount": 2200.0, "Old_Amount": 1400.0, "Spl_Amount": 1400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000639", "Test_Name": "FACTOR IX ACTIVITY", "Test_Amount": 1900.0, "Old_Amount": 1450.0, "Spl_Amount": 1450.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000640", "Test_Name": "FACTOR VIII FUNCTIONAL", "Test_Amount": 2500.0, "Old_Amount": 2100.0, "Spl_Amount": 2100.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000641", "Test_Name": "Beta-2-microglobulin", "Test_Amount": 1500.0, "Old_Amount": 1150.0, "Spl_Amount": 1150.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000642", "Test_Name": "GASTRIN", "Test_Amount": 1200.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000643", "Test_Name": "INSULIN (R)", "Test_Amount": 900.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000644", "Test_Name": "Prenatal Screening - 1st TRIMESTER", "Test_Amount": 0.0, "Old_Amount": 1250.0, "Spl_Amount": 1250.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000646", "Test_Name": "PROTEIN-C Activity", "Test_Amount": 2600.0, "Old_Amount": 2300.0, "Spl_Amount": 2300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000647", "Test_Name": "PROTEIN-S Activity", "Test_Amount": 2600.0, "Old_Amount": 2300.0, "Spl_Amount": 2300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000648", "Test_Name": "1 25 Dihydroxyvitamin D", "Test_Amount": 3500.0, "Old_Amount": 2700.0, "Spl_Amount": 2700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000650", "Test_Name": "NT pro B-type natriuretic peptide", "Test_Amount": 2100.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000651", "Test_Name": "IMMUNOFIXATION QUALITATIVE", "Test_Amount": 5000.0, "Old_Amount": 4000.0, "Spl_Amount": 4000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000653", "Test_Name": "5 Alpha  DHT - Di hydro testosterone", "Test_Amount": 1700.0, "Old_Amount": 1550.0, "Spl_Amount": 1550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000655", "Test_Name": "Free Androgen Index (FAI)", "Test_Amount": 2800.0, "Old_Amount": 2400.0, "Spl_Amount": 2400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000656", "Test_Name": "<PERSON><PERSON><PERSON> (Free)", "Test_Amount": 1500.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000657", "Test_Name": "Mullerian Inhibiting Substance - AMH", "Test_Amount": 1700.0, "Old_Amount": 1400.0, "Spl_Amount": 1400.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000658", "Test_Name": "C1 Esterase Inhibitor", "Test_Amount": 2300.0, "Old_Amount": 1900.0, "Spl_Amount": 1900.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000659", "Test_Name": "Procalcitonin", "Test_Amount": 2000.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000660", "Test_Name": "TSH Receptor antibody", "Test_Amount": 3000.0, "Old_Amount": 3200.0, "Spl_Amount": 3200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000661", "Test_Name": "PAPPa.", "Test_Amount": 1500.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000662", "Test_Name": "Erythropoietin", "Test_Amount": 1500.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000663", "Test_Name": "Islet cell anibody (ICA-512)", "Test_Amount": 2500.0, "Old_Amount": 2100.0, "Spl_Amount": 2100.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000664", "Test_Name": "JAK - 2 Mutation", "Test_Amount": 6000.0, "Old_Amount": 5000.0, "Spl_Amount": 5000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000665", "Test_Name": "CA 72.4 ( TAG-72)", "Test_Amount": 1700.0, "Old_Amount": 1350.0, "Spl_Amount": 1350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000666", "Test_Name": "Leptin (Human Leptin)", "Test_Amount": 5000.0, "Old_Amount": 4000.0, "Spl_Amount": 4000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000667", "Test_Name": "Bcr-Abl (PHILADELPHIA) t(9 22)", "Test_Amount": 5500.0, "Old_Amount": 4800.0, "Spl_Amount": 4800.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000669", "Test_Name": "NEWBORN SCREENING - BASIC", "Test_Amount": 1700.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000670", "Test_Name": "Free Beta HCG.", "Test_Amount": 900.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000671", "Test_Name": "CORTISOL (PM)", "Test_Amount": 800.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000672", "Test_Name": "Renin Activity (PRA)", "Test_Amount": 3500.0, "Old_Amount": 3000.0, "Spl_Amount": 3000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000673", "Test_Name": "RENIN DIRECT PLASMA", "Test_Amount": 3000.0, "Old_Amount": 2600.0, "Spl_Amount": 2600.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000674", "Test_Name": "Macro Prolactin", "Test_Amount": 900.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000675", "Test_Name": "TESTOSTERONE BINDING <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Test_Amount": 2000.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000676", "Test_Name": "FOLICACID (RED CELLS)", "Test_Amount": 2500.0, "Old_Amount": 1700.0, "Spl_Amount": 1700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000678", "Test_Name": "Tacrolimus - Blood", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000679", "Test_Name": "Islet Cell Antobody", "Test_Amount": 2500.0, "Old_Amount": 2100.0, "Spl_Amount": 2100.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000680", "Test_Name": "VITAMIN C (ASCORBIC ACID)", "Test_Amount": 2500.0, "Old_Amount": 2200.0, "Spl_Amount": 2200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000681", "Test_Name": "INHIBIN - A", "Test_Amount": 1400.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000682", "Test_Name": "5-HIAA (Hydroxy Indole Acetic Acid)  Urine 24H", "Test_Amount": 4000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000683", "Test_Name": "FACTOR XIII  ACTIVITY", "Test_Amount": 1500.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000686", "Test_Name": "CD4  CD8 - RATIO", "Test_Amount": 1400.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000687", "Test_Name": "Vitamin B6   (Pyridoxine)", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000688", "Test_Name": "NMO with MOG Antibody", "Test_Amount": 4500.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000689", "Test_Name": "VITAMIN  B1  (THIAMINE)", "Test_Amount": 2900.0, "Old_Amount": 2700.0, "Spl_Amount": 2700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000690", "Test_Name": "Prenatal Screening - Quadruple Markers.", "Test_Amount": 2500.0, "Old_Amount": 2700.0, "Spl_Amount": 2700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000692", "Test_Name": "Dopamine", "Test_Amount": 3000.0, "Old_Amount": 2300.0, "Spl_Amount": 2300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000693", "Test_Name": "Insulin - 30 mts", "Test_Amount": 900.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000694", "Test_Name": "Insulin - 120mts", "Test_Amount": 900.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000695", "Test_Name": "ST-II  cardiac marker", "Test_Amount": 2000.0, "Old_Amount": 1800.0, "Spl_Amount": 1800.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000696", "Test_Name": "FACTOR V  FUNCTIONAL", "Test_Amount": 2200.0, "Old_Amount": 1800.0, "Spl_Amount": 1800.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000697", "Test_Name": "C-peptide  Random", "Test_Amount": 1100.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000698", "Test_Name": "Homocysteine  Urine - Qualitative", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000700", "Test_Name": "Ornithine Quantitative", "Test_Amount": 8000.0, "Old_Amount": 7500.0, "Spl_Amount": 7500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000701", "Test_Name": "Serotonin (5-Hydroxy Tryptamine)", "Test_Amount": 5000.0, "Old_Amount": 4000.0, "Spl_Amount": 4000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001305", "Test_Name": "<PERSON> Muller<PERSON> (AMH)", "Test_Amount": 1500.0, "Old_Amount": 1250.0, "Spl_Amount": 1250.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001306", "Test_Name": "COPPER- SPOT URINE", "Test_Amount": 1500.0, "Old_Amount": 1350.0, "Spl_Amount": 1350.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001313", "Test_Name": "IgG4 Sub Class", "Test_Amount": 4600.0, "Old_Amount": 4000.0, "Spl_Amount": 4000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001317", "Test_Name": "Mercury  Blood", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001319", "Test_Name": "PLA2 receptor antibody", "Test_Amount": 5500.0, "Old_Amount": 4500.0, "Spl_Amount": 4500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001320", "Test_Name": "Determination of  Chromium - Urine", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001324", "Test_Name": "Aminogram  Plasma(Quantitative)", "Test_Amount": 6600.0, "Old_Amount": 6000.0, "Spl_Amount": 6000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001330", "Test_Name": "Thyroxine Binding Globulin (TBG)", "Test_Amount": 6000.0, "Old_Amount": 5000.0, "Spl_Amount": 5000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001331", "Test_Name": "Chromogranin A", "Test_Amount": 7000.0, "Old_Amount": 6200.0, "Spl_Amount": 6200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001353", "Test_Name": "Leishmania (KALA AZAR) Ab IgG", "Test_Amount": 2000.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001357", "Test_Name": "Interleukin 6 (IL-6)", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001360", "Test_Name": "TNF (Tumour Necrosis Factor) Alpha", "Test_Amount": 4400.0, "Old_Amount": 3900.0, "Spl_Amount": 3900.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001362", "Test_Name": "Vitamin B2 (Riboflavin)", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001366", "Test_Name": "PLACENTAL GROWTH FACTOR (PIGF)", "Test_Amount": 4300.0, "Old_Amount": 3800.0, "Spl_Amount": 3800.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001429", "Test_Name": "Activated Protein C Resisitance", "Test_Amount": 3900.0, "Old_Amount": 3300.0, "Spl_Amount": 3300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001499", "Test_Name": "Estrone", "Test_Amount": 6500.0, "Old_Amount": 6000.0, "Spl_Amount": 6000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001504", "Test_Name": "LDH- ISOENZYMES", "Test_Amount": 4500.0, "Old_Amount": 4000.0, "Spl_Amount": 4000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001507", "Test_Name": "Vancomycin.", "Test_Amount": 7000.0, "Old_Amount": 6000.0, "Spl_Amount": 6000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001514", "Test_Name": "ALDOSTERONE  PLASMA RENIN (DIRECT) RATIO", "Test_Amount": 7200.0, "Old_Amount": 6700.0, "Spl_Amount": 6700.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001515", "Test_Name": "Vitamin B Profile", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001548", "Test_Name": "NMO (Aquaporin 4) Neuromylitis Optica Ab", "Test_Amount": 4800.0, "Old_Amount": 4300.0, "Spl_Amount": 4300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001564", "Test_Name": "PHENOBARBITONE  Animal", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001572", "Test_Name": "DESMOGLEIN  I Antibody", "Test_Amount": 3200.0, "Old_Amount": 2650.0, "Spl_Amount": 2650.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001573", "Test_Name": "DESMOGLEIN  III Antibody", "Test_Amount": 3200.0, "Old_Amount": 2650.0, "Spl_Amount": 2650.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001587", "Test_Name": "VITAMIN  K", "Test_Amount": 1500.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001589", "Test_Name": "HLA Typing by PCR", "Test_Amount": 6000.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001612", "Test_Name": "CD20", "Test_Amount": 2500.0, "Old_Amount": 2200.0, "Spl_Amount": 2200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001620", "Test_Name": "Holotranscobalamin (Active Vitamin B12)", "Test_Amount": 1600.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001621", "Test_Name": "HLA-DR4 (DRB1*04)  DISEASE ASSOCIATION", "Test_Amount": 5000.0, "Old_Amount": 4300.0, "Spl_Amount": 4300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001629", "Test_Name": "HLA- DR2 (DRB1*15/16)  DISEASE ASSOCIATION", "Test_Amount": 5000.0, "Old_Amount": 4300.0, "Spl_Amount": 4300.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001630", "Test_Name": "PIIINP (PROCOLLAGEN TYPE III AMINO TERMINAL PROPEPTIDE)", "Test_Amount": 5000.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001632", "Test_Name": "Levetiracetam", "Test_Amount": 6000.0, "Old_Amount": 5150.0, "Spl_Amount": 5150.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001726", "Test_Name": "Double Marker Test", "Test_Amount": 2200.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00005", "Test_Name": "Thyroid function tests", "Test_Amount": 400.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00007", "Test_Name": "FREE TFT", "Test_Amount": 500.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00013", "Test_Name": "THYRO COMBO", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00019", "Test_Name": "FSH-LH-PRL-TSH", "Test_Amount": 1500.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@IM", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00031", "Test_Name": "Prenatal Screening - Quadruple Markers", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000702", "Test_Name": "PROJECT SAMPLE.", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000706", "Test_Name": "ACID FAST STAINING (AFB)", "Test_Amount": 75.0, "Old_Amount": 75.0, "Spl_Amount": 75.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000709", "Test_Name": "AFB - SYNOVIAL FLUID", "Test_Amount": 75.0, "Old_Amount": 75.0, "Spl_Amount": 75.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000723", "Test_Name": "<PERSON>'s stain", "Test_Amount": 200.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000731", "Test_Name": "C/S AFB", "Test_Amount": 350.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000733", "Test_Name": "C/S AFB (SPUTUM)", "Test_Amount": 300.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000734", "Test_Name": "AFB Culture (Rapid Isolation)", "Test_Amount": 900.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000735", "Test_Name": "C/S AFB ASCETIC FLUID", "Test_Amount": 350.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000736", "Test_Name": "C/S AFB- URINE", "Test_Amount": 300.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000743", "Test_Name": "C/S ACANTHAMOEBA", "Test_Amount": 250.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000744", "Test_Name": "C/S OTHERS.", "Test_Amount": 200.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000758", "Test_Name": "C/S CSF", "Test_Amount": 200.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000759", "Test_Name": "C/S CERVICAL SWAB", "Test_Amount": 130.0, "Old_Amount": 130.0, "Spl_Amount": 130.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000777", "Test_Name": "C/S SUCTION TUBE", "Test_Amount": 170.0, "Old_Amount": 180.0, "Spl_Amount": 180.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000778", "Test_Name": "Culture for Fungus", "Test_Amount": 300.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000782", "Test_Name": "C/S LABOUR ROOM", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000786", "Test_Name": "C/S Stool", "Test_Amount": 450.0, "Old_Amount": 180.0, "Spl_Amount": 180.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000791", "Test_Name": "C/S OT SWAB", "Test_Amount": 0.0, "Old_Amount": 2200.0, "Spl_Amount": 2200.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000798", "Test_Name": "Culture & Sensitivity - Pus", "Test_Amount": 450.0, "Old_Amount": 180.0, "Spl_Amount": 180.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000804", "Test_Name": "C/S SEMEN", "Test_Amount": 450.0, "Old_Amount": 180.0, "Spl_Amount": 180.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000806", "Test_Name": "C/S SPUTUM", "Test_Amount": 450.0, "Old_Amount": 180.0, "Spl_Amount": 180.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000810", "Test_Name": "C/S SWAB", "Test_Amount": 170.0, "Old_Amount": 180.0, "Spl_Amount": 180.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000811", "Test_Name": "C/S SYNOVIAL FLUID", "Test_Amount": 170.0, "Old_Amount": 180.0, "Spl_Amount": 180.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000813", "Test_Name": "THEATRE SWAB", "Test_Amount": 1800.0, "Old_Amount": 1800.0, "Spl_Amount": 1800.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000816", "Test_Name": "C/S THROAT SWAB", "Test_Amount": 170.0, "Old_Amount": 180.0, "Spl_Amount": 180.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000818", "Test_Name": "C/S TISSUE", "Test_Amount": 170.0, "Old_Amount": 180.0, "Spl_Amount": 180.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000821", "Test_Name": "URINE Culture & Sensitivity", "Test_Amount": 450.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000822", "Test_Name": "C/S VAGINAL SWAB", "Test_Amount": 170.0, "Old_Amount": 180.0, "Spl_Amount": 180.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000826", "Test_Name": "WATER CULTURE - AUTOMATED", "Test_Amount": 500.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000828", "Test_Name": "FLUID BACT AFB CULTURE", "Test_Amount": 800.0, "Old_Amount": 800.0, "Spl_Amount": 800.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000829", "Test_Name": "FUNGAL - SMEAR", "Test_Amount": 100.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000831", "Test_Name": "GONOCOCCI", "Test_Amount": 100.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000832", "Test_Name": "GRAM STAIN - PUS", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000833", "Test_Name": "GRAM STAIN - URINE", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000834", "Test_Name": "GRAM STAIN ASPIRATION FLUID", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000835", "Test_Name": "GRAM STAINING", "Test_Amount": 60.0, "Old_Amount": 60.0, "Spl_Amount": 60.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000836", "Test_Name": "GRAM STAINING - PLEURAL FLUID", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000837", "Test_Name": "Gram Staining - Vaginal Swab", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000838", "Test_Name": "GRAMSTAIN - PERICARDIAL FLUID", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000839", "Test_Name": "GRAMSTAIN - SYNOVIAL", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000840", "Test_Name": "GRAMSTAIN CORNEAL ULCER", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000841", "Test_Name": "GRAMSTAIN SPUTUM", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000842", "Test_Name": "GRAMSTAIN- CSF", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000843", "Test_Name": "GRAMSTAIN- THROAT SWAB", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000844", "Test_Name": "GRAM STAIN", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000845", "Test_Name": "HANGING DROP", "Test_Amount": 300.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000846", "Test_Name": "INDIA INK PREPRATION", "Test_Amount": 100.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000854", "Test_Name": "NAIL CLIPPING FOR FUNGUS", "Test_Amount": 75.0, "Old_Amount": 75.0, "Spl_Amount": 75.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000857", "Test_Name": "SCRAPING FOR FUNGUS", "Test_Amount": 100.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000867", "Test_Name": "SPUTUM PCR", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000869", "Test_Name": "SYNOVIAL FLUID AFB", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000870", "Test_Name": "SYNOVIAL FLUID GRAM STAIN", "Test_Amount": 50.0, "Old_Amount": 50.0, "Spl_Amount": 50.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000880", "Test_Name": "URINE AFB (24 HR)", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000881", "Test_Name": "URINE- AFB", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000890", "Test_Name": "Enumeration of Aerobic Bacteria", "Test_Amount": 1900.0, "Old_Amount": 1900.0, "Spl_Amount": 1900.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000891", "Test_Name": "Enumeration of Propini Bacterium", "Test_Amount": 200.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000892", "Test_Name": "Enumeration of Cellulolytic Bacteria (Aerobic)", "Test_Amount": 200.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000893", "Test_Name": "BLOOD CULTURE & SENSITIVITY", "Test_Amount": 1000.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000894", "Test_Name": "BLOOD CULTURE (FINAL)", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000895", "Test_Name": "WATER ANALYSIS (MPN) FOR FAECAL CONTAMINATION", "Test_Amount": 200.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000896", "Test_Name": "WATER ANALYSIS (MEMBRANE FILTER METHOD)", "Test_Amount": 200.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000897", "Test_Name": "C/S WATER BORN PATHOGENS (POULTARY)", "Test_Amount": 225.0, "Old_Amount": 225.0, "Spl_Amount": 225.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000898", "Test_Name": "Bacteria Identification (Aerobes)", "Test_Amount": 750.0, "Old_Amount": 750.0, "Spl_Amount": 750.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000899", "Test_Name": "Bacteria Identification (Aerobes).", "Test_Amount": 1000.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000900", "Test_Name": "GEIMSA STAINING", "Test_Amount": 40.0, "Old_Amount": 40.0, "Spl_Amount": 40.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000901", "Test_Name": "C/S OPEN-END SPECIMEN", "Test_Amount": 2000.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000903", "Test_Name": "Enumeration of Photosynthetic Bacteria", "Test_Amount": 500.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000904", "Test_Name": "Enumeration of Lactic Acid Bacteria", "Test_Amount": 250.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000905", "Test_Name": "Enumeration of Fermentative Yeast Fungi", "Test_Amount": 250.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000906", "Test_Name": "Enumeration of Actinomycetes", "Test_Amount": 250.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000907", "Test_Name": "GIEMSA STAINING", "Test_Amount": 40.0, "Old_Amount": 40.0, "Spl_Amount": 40.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000909", "Test_Name": "Microbiology Surveillance Test", "Test_Amount": 1.0, "Old_Amount": 1.0, "Spl_Amount": 1.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000911", "Test_Name": "ACID FAST STAINING (AFB)  Sputum", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000912", "Test_Name": "Vaginal swab - wetmount", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000913", "Test_Name": "PCP STAINING", "Test_Amount": 100.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000920", "Test_Name": "Culture for AFB(MYCOBACTERIA) - Rapid", "Test_Amount": 900.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000921", "Test_Name": "AFB -DRUG SENSITIVITY 4 Drugs", "Test_Amount": 3000.0, "Old_Amount": 3000.0, "Spl_Amount": 3000.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001361", "Test_Name": "ODC PACKAGE", "Test_Amount": 900.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001416", "Test_Name": "ROTA/ADENOVIRUS", "Test_Amount": 650.0, "Old_Amount": 650.0, "Spl_Amount": 650.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001667", "Test_Name": "STOOL CALPROTECTIN", "Test_Amount": 3500.0, "Old_Amount": 3200.0, "Spl_Amount": 3200.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001669", "Test_Name": "WOUND SWAB CULTURE & SENSITIVITY", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001672", "Test_Name": "MAJOR OT SWAB", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001673", "Test_Name": "MINOR OT SWAB", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001727", "Test_Name": "HAIR ROOT FUNGUS", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001731", "Test_Name": "HAIR STUDY FOR KERATIN", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00011", "Test_Name": "C/S BLOOD-Enteric & Non enteric (Manual)", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MB", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00018", "Test_Name": "C/S BLOOD-Enteric & Non Enteric (Automated)", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000923", "Test_Name": "CMV - Cytomegalovirus  PCR (Quantitative)", "Test_Amount": 6500.0, "Old_Amount": 5900.0, "Spl_Amount": 5900.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000924", "Test_Name": "THALASSEMIA - BETA (5 COMMON MUTATION) BLOOD", "Test_Amount": 8000.0, "Old_Amount": 8000.0, "Spl_Amount": 8000.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000925", "Test_Name": "Epstein-Barr Virus (EBV) DNA", "Test_Amount": 7000.0, "Old_Amount": 6000.0, "Spl_Amount": 6000.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000926", "Test_Name": "CMV-DNA PCR (Qualitative)", "Test_Amount": 5000.0, "Old_Amount": 4500.0, "Spl_Amount": 4500.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000927", "Test_Name": "Hepatitis B Genotyping (HBV)", "Test_Amount": 6500.0, "Old_Amount": 5500.0, "Spl_Amount": 5500.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000928", "Test_Name": "MTHFR Mutations", "Test_Amount": 7500.0, "Old_Amount": 7000.0, "Spl_Amount": 7000.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000929", "Test_Name": "HIV-1 Proviral DNA (Qualitative)", "Test_Amount": 3900.0, "Old_Amount": 3200.0, "Spl_Amount": 3200.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000930", "Test_Name": "Y Chromosome Microdeletion", "Test_Amount": 8500.0, "Old_Amount": 7800.0, "Spl_Amount": 7800.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000931", "Test_Name": "HBV - DNA Qualitative", "Test_Amount": 3500.0, "Old_Amount": 3000.0, "Spl_Amount": 3000.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000932", "Test_Name": "HBV DNA - Quantitative", "Test_Amount": 5500.0, "Old_Amount": 5000.0, "Spl_Amount": 5000.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000933", "Test_Name": "HIV  RNA (Quantitative)", "Test_Amount": 3900.0, "Old_Amount": 3700.0, "Spl_Amount": 3700.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000934", "Test_Name": "HIV-RNA QUALITATIVE", "Test_Amount": 2200.0, "Old_Amount": 1900.0, "Spl_Amount": 1900.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000935", "Test_Name": "TB PCR (<PERSON>.<PERSON> by Gene<PERSON>pert)", "Test_Amount": 2200.0, "Old_Amount": 1700.0, "Spl_Amount": 1700.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001098", "Test_Name": "CHIKUNGUNYA   Qualitative PCR", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001132", "Test_Name": "HCV- RNA PCR (Qualitative)", "Test_Amount": 3500.0, "Old_Amount": 3000.0, "Spl_Amount": 3000.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001133", "Test_Name": "HCV- RNA PCR (Quantitative)", "Test_Amount": 6000.0, "Old_Amount": 4500.0, "Spl_Amount": 4500.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001144", "Test_Name": "JAPANESE ENCEPHALITIS VIRUS DETECTION  PCR", "Test_Amount": 5000.0, "Old_Amount": 4300.0, "Spl_Amount": 4300.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001184", "Test_Name": "TB PCR (BLOOD)", "Test_Amount": 2200.0, "Old_Amount": 1700.0, "Spl_Amount": 1700.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001228", "Test_Name": "TOXOPLASMA DNA PCR", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001230", "Test_Name": "HSV DNA (1 & 2) QUALITATIVE PCR", "Test_Amount": 3200.0, "Old_Amount": 2900.0, "Spl_Amount": 2900.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001235", "Test_Name": "Human Papillomavirus  DNA - PCR", "Test_Amount": 2500.0, "Old_Amount": 2300.0, "Spl_Amount": 2300.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001241", "Test_Name": "HCV-GENOTYPING", "Test_Amount": 7000.0, "Old_Amount": 6700.0, "Spl_Amount": 6700.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001322", "Test_Name": "Parvovirus B19  DNA PCR", "Test_Amount": 3200.0, "Old_Amount": 2700.0, "Spl_Amount": 2700.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001356", "Test_Name": "HLA B27 (PCR)", "Test_Amount": 4000.0, "Old_Amount": 3100.0, "Spl_Amount": 3100.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001511", "Test_Name": "CMV-DNA PCR (Qualitative)  Urine", "Test_Amount": 5000.0, "Old_Amount": 4500.0, "Spl_Amount": 4500.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001527", "Test_Name": "Malaria Parasite   PCR", "Test_Amount": 3300.0, "Old_Amount": 2800.0, "Spl_Amount": 2800.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001534", "Test_Name": "Neurotropic Virus panel  PCR", "Test_Amount": 6300.0, "Old_Amount": 5800.0, "Spl_Amount": 5800.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001535", "Test_Name": "HIV-1 Drug Resistance – Extended (PI  NRTI  NNRTI  INSTI)", "Test_Amount": 18000.0, "Old_Amount": 17000.0, "Spl_Amount": 17000.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001566", "Test_Name": "SARS-CoV-2 (COVID-19)", "Test_Amount": 900.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001623", "Test_Name": "CHLAMYDIA TRACHOMATIS - PCR Qualitative", "Test_Amount": 4000.0, "Old_Amount": 3500.0, "Spl_Amount": 3500.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001626", "Test_Name": "PML-RARA GENE REARRANGEMENT  QUALITATIVE", "Test_Amount": 5700.0, "Old_Amount": 5200.0, "Spl_Amount": 5200.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001653", "Test_Name": "Cystic Fibrosis Genetic Screening", "Test_Amount": 13000.0, "Old_Amount": 11500.0, "Spl_Amount": 11500.0}, {"Dept_Code": "@MO", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001735", "Test_Name": "NEISSERIA GONORRHOEA QUALITATIVE BY REAL TIME PCR", "Test_Amount": 5500.0, "Old_Amount": 3000.0, "Spl_Amount": 3000.0}, {"Dept_Code": "@MS", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000738", "Test_Name": "Microbiology Surveillance Test (Anaerobic)", "Test_Amount": 400.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@MS", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001346", "Test_Name": "Water Culture (Direct & Enrichment)", "Test_Amount": 500.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@MS", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001347", "Test_Name": "Microbiology Surveillance Test (1 Swab)", "Test_Amount": 175.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@MS", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001363", "Test_Name": "Open End Specimen", "Test_Amount": 175.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@MS", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001555", "Test_Name": "C/S - OTHERS.", "Test_Amount": 450.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000946", "Test_Name": "Fusarium Oxysporum Culture filtrate preparation", "Test_Amount": 1000.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000947", "Test_Name": "Syringe & Needle", "Test_Amount": 5.0, "Old_Amount": 4.0, "Spl_Amount": 4.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000948", "Test_Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Test_Amount": 5.0, "Old_Amount": 6.0, "Spl_Amount": 6.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000949", "Test_Name": "<PERSON><PERSON>tainer", "Test_Amount": 5.0, "Old_Amount": 5.0, "Spl_Amount": 5.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000951", "Test_Name": "Paediatric Phlebotomy charge", "Test_Amount": 8300.0, "Old_Amount": 8300.0, "Spl_Amount": 8300.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000955", "Test_Name": "Screening for Anthrax bacilli", "Test_Amount": 200.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000956", "Test_Name": "Screening for clostridial spores", "Test_Amount": 500.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000957", "Test_Name": "Formaldehyde Analysis", "Test_Amount": 4000.0, "Old_Amount": 4000.0, "Spl_Amount": 4000.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000958", "Test_Name": "Isolation of microorganisms from 2 soil sample", "Test_Amount": 1500.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001349", "Test_Name": "Weight", "Test_Amount": 20.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001417", "Test_Name": "Consumables for Franchisee", "Test_Amount": 850.0, "Old_Amount": 3518.0, "Spl_Amount": 3518.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001437", "Test_Name": "Height", "Test_Amount": 20.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001439", "Test_Name": "Body Mass Index", "Test_Amount": 20.0, "Old_Amount": 20.0, "Spl_Amount": 20.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001569", "Test_Name": "Sample Collection  Charges", "Test_Amount": 200.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001633", "Test_Name": "Blood Pressure", "Test_Amount": 20.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001644", "Test_Name": "MEDICAL FITNESS", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@OH", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00041", "Test_Name": "FREEDOM HEALTH PACKAGE", "Test_Amount": 1000.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000975", "Test_Name": "ECG", "Test_Amount": 250.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@000976", "Test_Name": "ECG WITH RYTHM STRIP", "Test_Amount": 90.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001027", "Test_Name": "TMT", "Test_Amount": 850.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001028", "Test_Name": "Ultrasound Abdomen", "Test_Amount": 400.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001029", "Test_Name": "P N S", "Test_Amount": 300.0, "Old_Amount": 120.0, "Spl_Amount": 120.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001030", "Test_Name": "PNS", "Test_Amount": 300.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001031", "Test_Name": "KNEE A.P AND LATERAL - Left", "Test_Amount": 450.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001307", "Test_Name": "BOTH ANKLE AP/LAT", "Test_Amount": 900.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001308", "Test_Name": "BOTH HAND AP VIEW", "Test_Amount": 450.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001309", "Test_Name": "COCCYX", "Test_Amount": 450.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001310", "Test_Name": "LUMBAR & THORACIC SPINE AP/LAT", "Test_Amount": 450.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001311", "Test_Name": "NASOPHARYNX FOR ADENOID", "Test_Amount": 300.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001312", "Test_Name": "SKULL AP/LAT", "Test_Amount": 300.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001545", "Test_Name": "Audiogram", "Test_Amount": 200.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001561", "Test_Name": "Pulmonary Function Test", "Test_Amount": 200.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001671", "Test_Name": "X-RAY- Chest", "Test_Amount": 500.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001705", "Test_Name": "HAND AP / OBLIQUE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001706", "Test_Name": "WRIST AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001707", "Test_Name": "FOREARM AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001708", "Test_Name": "ELBOW AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001709", "Test_Name": "HUMERUS AP/ LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001710", "Test_Name": "SHOULDER AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001711", "Test_Name": "CLAVICLE AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001712", "Test_Name": "SKULL AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001713", "Test_Name": "C-SPINE AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001714", "Test_Name": "D.L SPINE AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001715", "Test_Name": "L.S. SPINE AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001716", "Test_Name": "BOTH HAND AP / OBLIQUE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001717", "Test_Name": "FOOT - AP/ OBLIQUE", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001718", "Test_Name": "LEG AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001719", "Test_Name": "KNEE JOINT AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001720", "Test_Name": "FEMUR AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001721", "Test_Name": "PELVIS WITH BOTH HIP", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001722", "Test_Name": "ANKLE AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001723", "Test_Name": "BOTH ANKLE AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001724", "Test_Name": "PNS VIEW", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001725", "Test_Name": "BOTH KNEE STANDING AP / LATERAL", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001728", "Test_Name": "ABDOMEN ERACT X RAY", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001729", "Test_Name": "X-RAY SPECIAL VIEW", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@RA", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001732", "Test_Name": "SHOULDER AP VIEW", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001035", "Test_Name": "ANCA", "Test_Amount": 1800.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001037", "Test_Name": "P-ANCA (MPO)", "Test_Amount": 900.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001038", "Test_Name": "C-ANCA (PR3)", "Test_Amount": 900.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001039", "Test_Name": "ASO - Antistreptolysin O", "Test_Amount": 500.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001040", "Test_Name": "Allergen - <PERSON><PERSON><PERSON><PERSON> (Fungus)", "Test_Amount": 1200.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001041", "Test_Name": "AMOEBIC ANTIBODY- IgG", "Test_Amount": 1800.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001042", "Test_Name": "ANA", "Test_Amount": 800.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001044", "Test_Name": "ANA INDEX", "Test_Amount": 1.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001045", "Test_Name": "Anti Nuclear Antibody (IF)", "Test_Amount": 900.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001046", "Test_Name": "Anti dsDNA", "Test_Amount": 900.0, "Old_Amount": 550.0, "Spl_Amount": 550.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001047", "Test_Name": "ANTI  HBe", "Test_Amount": 800.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001048", "Test_Name": "ANTI  LEPTOSPIRAL ANTIBODY IgM", "Test_Amount": 700.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001050", "Test_Name": "Anti-La/SSB Antibody", "Test_Amount": 1200.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001052", "Test_Name": "ANTI CARDIO LIPIN ANTIBODY IgA", "Test_Amount": 900.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001053", "Test_Name": "ANTI CARDIOLIPIN ANTIBODY IgG", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001054", "Test_Name": "ANTI CARDIOLIPIN ANTIBODY IgM", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001055", "Test_Name": "ANTI CCP", "Test_Amount": 1400.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001057", "Test_Name": "Anti SS DNA", "Test_Amount": 2900.0, "Old_Amount": 2300.0, "Spl_Amount": 2300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001058", "Test_Name": "ANTI HAV - IgG", "Test_Amount": 1500.0, "Old_Amount": 1100.0, "Spl_Amount": 1100.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001059", "Test_Name": "ANTI HAV -TOTAL", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001060", "Test_Name": "ANTI HAV IgM", "Test_Amount": 800.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001062", "Test_Name": "ANTI HBc Total", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001063", "Test_Name": "ANTI HBc IgM", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001064", "Test_Name": "ANTI HBs", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001065", "Test_Name": "ANTI HCV", "Test_Amount": 900.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001067", "Test_Name": "ANTI HCV IgM", "Test_Amount": 1700.0, "Old_Amount": 1100.0, "Spl_Amount": 1100.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001068", "Test_Name": "ANTI HEV IgG", "Test_Amount": 1500.0, "Old_Amount": 1100.0, "Spl_Amount": 1100.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001069", "Test_Name": "ANTI HEV IgM", "Test_Amount": 850.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001070", "Test_Name": "ANTI HIV Ag/Ab Combo", "Test_Amount": 900.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001071", "Test_Name": "ANTI HIV 1&2 (CARD)", "Test_Amount": 400.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001072", "Test_Name": "HIV p24 ANTIGEN", "Test_Amount": 1500.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001075", "Test_Name": "ANTI MALARIAL ANTIBODY IgG", "Test_Amount": 700.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001076", "Test_Name": "MITOCHONDRIAL ANTIBODIES (AMA)", "Test_Amount": 1200.0, "Old_Amount": 850.0, "Spl_Amount": 850.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001077", "Test_Name": "ANTI PHOSPHOLIPID ANTIBODY IgG", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001078", "Test_Name": "ANTI PHOSPHOLIPID ANTIBODY IgM", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001079", "Test_Name": "ANTI SCL 70", "Test_Amount": 1400.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001081", "Test_Name": "SMOOTH MUSCLE ANTIBODY (ASMA)", "Test_Amount": 1400.0, "Old_Amount": 1250.0, "Spl_Amount": 1250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001083", "Test_Name": "ANTI SPERM ANTIBODY", "Test_Amount": 750.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001088", "Test_Name": "Aspergillosis Antibody - IgG", "Test_Amount": 2000.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001089", "Test_Name": "Aspergillosis Antibody - IgM", "Test_Amount": 2000.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001090", "Test_Name": "BRUCELLA  ANTIBODY - IGM", "Test_Amount": 900.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001092", "Test_Name": "BRUCELLA ANTIBODY - IGG", "Test_Amount": 900.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001097", "Test_Name": "CHIKUNGUNYA IgM (CARD)", "Test_Amount": 800.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001099", "Test_Name": "CHLAMYDIAL ANTIBODY  IgM", "Test_Amount": 1500.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001100", "Test_Name": "CHLAMYDIAL ANTIBODY IgG", "Test_Amount": 1500.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001101", "Test_Name": "CMV IgG", "Test_Amount": 600.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001102", "Test_Name": "CMV IgM", "Test_Amount": 600.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001104", "Test_Name": "CRYPTOCOCCUS ANTIGEN", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001114", "Test_Name": "DENGUE ANTIBODY IgG", "Test_Amount": 500.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001115", "Test_Name": "DENGUE ANTIBODY IgM", "Test_Amount": 500.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001118", "Test_Name": "FTA-Abs IgG", "Test_Amount": 1500.0, "Old_Amount": 1100.0, "Spl_Amount": 1100.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001120", "Test_Name": "GERMAN MEASLES (RUBELLA IGG)", "Test_Amount": 1300.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001121", "Test_Name": "Helicobacter Pylori -IgM antibodies", "Test_Amount": 1700.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001122", "Test_Name": "Helicobacter Pylori-IgG antibodies", "Test_Amount": 1300.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001125", "Test_Name": "HAV IGM-Antibody", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001126", "Test_Name": "Anti-HBc Total (Ab to Hep-B Core Ag)", "Test_Amount": 1200.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001127", "Test_Name": "Hbe Ag", "Test_Amount": 900.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001128", "Test_Name": "HBsAg (CARD)", "Test_Amount": 200.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001129", "Test_Name": "HBsAg", "Test_Amount": 350.0, "Old_Amount": 170.0, "Spl_Amount": 170.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001130", "Test_Name": "HBsAg Confirmation", "Test_Amount": 800.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001135", "Test_Name": "HSV  I (IgG)", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001136", "Test_Name": "HSV 1 & 2 IgG", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001137", "Test_Name": "HSV 1 & 2 IgM", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001138", "Test_Name": "HSV II (IgG)", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001140", "Test_Name": "HSV I (IgM)", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001145", "Test_Name": "LYMES ANTIBODY", "Test_Amount": 3000.0, "Old_Amount": 2400.0, "Spl_Amount": 2400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001146", "Test_Name": "<PERSON><PERSON><PERSON> IgG  <PERSON>", "Test_Amount": 1200.0, "Old_Amount": 800.0, "Spl_Amount": 800.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001147", "Test_Name": "MEASLES (Rubeola) - ANTIBODY IgM", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001148", "Test_Name": "Mumps IgG", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001149", "Test_Name": "MUMPS IgM", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001151", "Test_Name": "Mycoplasma pneumoniae - IgM", "Test_Amount": 2000.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001153", "Test_Name": "P C R  - Hepatitis B", "Test_Amount": 3800.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001154", "Test_Name": "PAUL BUNNEL TEST (IM - SCREENING)", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001158", "Test_Name": "RA (ELISA)", "Test_Amount": 1.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001159", "Test_Name": "RA FACTOR-LATEX", "Test_Amount": 400.0, "Old_Amount": 175.0, "Spl_Amount": 175.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001162", "Test_Name": "RA - FACTOR", "Test_Amount": 400.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001166", "Test_Name": "Ricketssia by PCR", "Test_Amount": 5500.0, "Old_Amount": 5000.0, "Spl_Amount": 5000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001167", "Test_Name": "RPR (Rapid Plasma Reagin)", "Test_Amount": 350.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001168", "Test_Name": "RUBELLA IgG", "Test_Amount": 800.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001169", "Test_Name": "RUBELLA IGM", "Test_Amount": 800.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001170", "Test_Name": "MEASLES (Rubeola) IgG", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001171", "Test_Name": "S S A ANTI R-O ANTIBODY", "Test_Amount": 1800.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001172", "Test_Name": "S S B-La ANTIBODY", "Test_Amount": 1800.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001174", "Test_Name": "SMITH ANTIBODY (SM)", "Test_Amount": 1500.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001191", "Test_Name": "Interferon Gamma Release Assay - TB", "Test_Amount": 2800.0, "Old_Amount": 2300.0, "Spl_Amount": 2300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001193", "Test_Name": "TORCH IgG", "Test_Amount": 1500.0, "Old_Amount": 850.0, "Spl_Amount": 850.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001194", "Test_Name": "TORCH IgM", "Test_Amount": 1500.0, "Old_Amount": 850.0, "Spl_Amount": 850.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001195", "Test_Name": "TOXOPLASMA IgG", "Test_Amount": 600.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001196", "Test_Name": "TOXOPLASMA IgM", "Test_Amount": 600.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001197", "Test_Name": "Treponema Pallidum Hemagglutination (TPHA)", "Test_Amount": 350.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001198", "Test_Name": "U1RNP Antibody", "Test_Amount": 1500.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001199", "Test_Name": "Varicella Z<PERSON>er -IgM antibodies", "Test_Amount": 900.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001200", "Test_Name": "Varicella - Zoster IgG", "Test_Amount": 1400.0, "Old_Amount": 1700.0, "Spl_Amount": 1700.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001201", "Test_Name": "<PERSON><PERSON>phi<PERSON> (VDRL)", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001202", "Test_Name": "WEIL FELIX TEST", "Test_Amount": 400.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001203", "Test_Name": "WESTERN BLOT", "Test_Amount": 2000.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001204", "Test_Name": "WIDAL - Tube Dilution", "Test_Amount": 150.0, "Old_Amount": 100.0, "Spl_Amount": 100.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001206", "Test_Name": "BRUCELLA AGGLUTINATION TEST", "Test_Amount": 500.0, "Old_Amount": 220.0, "Spl_Amount": 220.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001207", "Test_Name": "LEPTOSPIRA IgM", "Test_Amount": 700.0, "Old_Amount": 375.0, "Spl_Amount": 375.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001208", "Test_Name": "Anti DNase B", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001209", "Test_Name": "ANTI HBe", "Test_Amount": 800.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001210", "Test_Name": "ANTI HIV I & 2 (ELISA)", "Test_Amount": 1100.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001211", "Test_Name": "ANTI HCV (CARD)", "Test_Amount": 200.0, "Old_Amount": 200.0, "Spl_Amount": 200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001212", "Test_Name": "WIDAL-SLIDE METHOD", "Test_Amount": 150.0, "Old_Amount": 75.0, "Spl_Amount": 75.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001213", "Test_Name": "Rabies antibody", "Test_Amount": 4000.0, "Old_Amount": 3600.0, "Spl_Amount": 3600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001214", "Test_Name": "ANTI GLIADIN IgA", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001215", "Test_Name": "ANTI GLIADIN IgG", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001216", "Test_Name": "DIPHTHERIA IgG-Antibodies", "Test_Amount": 2900.0, "Old_Amount": 2400.0, "Spl_Amount": 2400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001217", "Test_Name": "Antibody to Sm Antigen", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001218", "Test_Name": "RNP-Sm Antibody", "Test_Amount": 1500.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001219", "Test_Name": "Jo-1 Antibody", "Test_Amount": 1400.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001220", "Test_Name": "Scl-70 Antibody IgG (DNA Topoisomerase-1)", "Test_Amount": 1400.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001221", "Test_Name": "Febrile Agglutination Test", "Test_Amount": 1000.0, "Old_Amount": 800.0, "Spl_Amount": 800.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001223", "Test_Name": "ANTI HDV IgM", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001224", "Test_Name": "ASCA - IgA", "Test_Amount": 1700.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001225", "Test_Name": "ASCA - IgG", "Test_Amount": 1700.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001226", "Test_Name": "CRP-LATEX", "Test_Amount": 400.0, "Old_Amount": 150.0, "Spl_Amount": 150.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001227", "Test_Name": "ANA PROFILE (Blot)", "Test_Amount": 3000.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001229", "Test_Name": "Influenza A & B", "Test_Amount": 800.0, "Old_Amount": 450.0, "Spl_Amount": 450.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001231", "Test_Name": "LEPTOSPIRA IgG", "Test_Amount": 900.0, "Old_Amount": 750.0, "Spl_Amount": 750.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001232", "Test_Name": "HISTONE ANTIBODY.", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001233", "Test_Name": "ANTI DIURETIC HORMONE-ADH", "Test_Amount": 3500.0, "Old_Amount": 3200.0, "Spl_Amount": 3200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001234", "Test_Name": "FTA-Abs IgM", "Test_Amount": 2000.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001236", "Test_Name": "LEPTOSPIRA BY MAT TEST", "Test_Amount": 600.0, "Old_Amount": 350.0, "Spl_Amount": 350.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001237", "Test_Name": "LKM1-Antibodies", "Test_Amount": 2000.0, "Old_Amount": 1700.0, "Spl_Amount": 1700.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001238", "Test_Name": "GBM - Glomerular Basement Membrane  Antibody", "Test_Amount": 1200.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001239", "Test_Name": "TREPONEMA ANTIBODIES", "Test_Amount": 1000.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001240", "Test_Name": "Tissue Transglutaminase (tTG) Ab-IgA", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001242", "Test_Name": "HAMS TEST", "Test_Amount": 1100.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001243", "Test_Name": "Echinococcus(Hydatid Cyst) IgG", "Test_Amount": 1500.0, "Old_Amount": 1250.0, "Spl_Amount": 1250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001244", "Test_Name": "DENGUE Ns1 ANTIGEN - Card", "Test_Amount": 600.0, "Old_Amount": 500.0, "Spl_Amount": 500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001245", "Test_Name": "Endomysial Antibody - IgA", "Test_Amount": 1800.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001246", "Test_Name": "DENGUE IgM (CARD)", "Test_Amount": 600.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001247", "Test_Name": "DENGUE IgG (CARD)", "Test_Amount": 600.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001248", "Test_Name": "Cryptococcus Antigen  CSF", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001249", "Test_Name": "LEPTOSPIRA By (DFM)", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001251", "Test_Name": "HSV II (IgM)", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001252", "Test_Name": "WESTERN BLOT (HIV-1)", "Test_Amount": 2000.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001253", "Test_Name": "DENGUE Ab/Ag - Card", "Test_Amount": 800.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001254", "Test_Name": "Treponema  Antibodies", "Test_Amount": 1000.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001255", "Test_Name": "Ovarian Antibody (AOA)", "Test_Amount": 2400.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001259", "Test_Name": "Parvovirus  (B19) IgG", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001260", "Test_Name": "Parvovirus (B19) IgM", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001261", "Test_Name": "EBV (NA) IgG", "Test_Amount": 1750.0, "Old_Amount": 1250.0, "Spl_Amount": 1250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001262", "Test_Name": "EBV (NA) IgM", "Test_Amount": 1750.0, "Old_Amount": 1250.0, "Spl_Amount": 1250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001263", "Test_Name": "EBV (VCA) IgG", "Test_Amount": 1750.0, "Old_Amount": 1250.0, "Spl_Amount": 1250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001264", "Test_Name": "EBV (VCA) IgM", "Test_Amount": 1750.0, "Old_Amount": 1250.0, "Spl_Amount": 1250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001266", "Test_Name": "EBV  - MONOSPOT", "Test_Amount": 700.0, "Old_Amount": 600.0, "Spl_Amount": 600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001269", "Test_Name": "LEPTOSPIRAL IgG & IgM (Card)", "Test_Amount": 600.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001270", "Test_Name": "Salmonella Typhi Dot - IgM", "Test_Amount": 400.0, "Old_Amount": 360.0, "Spl_Amount": 360.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001271", "Test_Name": "Toxoplasma  Avidity test", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001272", "Test_Name": "CMV - Avidity test", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001273", "Test_Name": "Rubella - Avidity test", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001274", "Test_Name": "Centromere Antibody IgG", "Test_Amount": 1700.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001275", "Test_Name": "Parietal Cell Antibody (GPCA)", "Test_Amount": 2200.0, "Old_Amount": 1800.0, "Spl_Amount": 1800.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001277", "Test_Name": "Mycoplasma pneumoniae - IgG", "Test_Amount": 2000.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001278", "Test_Name": "SCRUB TYPHUS IGM", "Test_Amount": 1200.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001279", "Test_Name": "DENGUE Ns1 ANTIGEN - CLIA", "Test_Amount": 500.0, "Old_Amount": 400.0, "Spl_Amount": 400.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001280", "Test_Name": "HBsAg (ELISA)", "Test_Amount": 1200.0, "Old_Amount": 725.0, "Spl_Amount": 725.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001281", "Test_Name": "Beta-2-glycoprotein I-IgG", "Test_Amount": 1200.0, "Old_Amount": 850.0, "Spl_Amount": 850.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001282", "Test_Name": "Beta-2-glycoprotein I-IgM", "Test_Amount": 1200.0, "Old_Amount": 850.0, "Spl_Amount": 850.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001283", "Test_Name": "Anti MAG (Myelin associated Glycoprotein)", "Test_Amount": 3500.0, "Old_Amount": 3200.0, "Spl_Amount": 3200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001284", "Test_Name": "Ganglioside antibody IgG (Anti GM1)", "Test_Amount": 4500.0, "Old_Amount": 4000.0, "Spl_Amount": 4000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001285", "Test_Name": "Allergy Profile (Drugs)", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001286", "Test_Name": "Antibodies to Soluble Liver Antigen (SLA)", "Test_Amount": 1500.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001287", "Test_Name": "Allergen - Milk", "Test_Amount": 1000.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001288", "Test_Name": "Allergens Food - Nuts", "Test_Amount": 6500.0, "Old_Amount": 6000.0, "Spl_Amount": 6000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001289", "Test_Name": "Allergen EGG Yolk", "Test_Amount": 1000.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001290", "Test_Name": "Allergen EGG White", "Test_Amount": 1000.0, "Old_Amount": 900.0, "Spl_Amount": 900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001291", "Test_Name": "Intrinsic factor antibody", "Test_Amount": 2000.0, "Old_Amount": 1900.0, "Spl_Amount": 1900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001292", "Test_Name": "Borrel<PERSON>(Lyme) - IgG Ab", "Test_Amount": 2000.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001293", "Test_Name": "Borrel<PERSON>(Lyme)- IgM Ab", "Test_Amount": 2000.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001294", "Test_Name": "Clostridium Difficile Toxin (A&B)", "Test_Amount": 2200.0, "Old_Amount": 1900.0, "Spl_Amount": 1900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001295", "Test_Name": "HTLV (I & II) Antibody", "Test_Amount": 5700.0, "Old_Amount": 4900.0, "Spl_Amount": 4900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001296", "Test_Name": "MUSK Antibody  Myasthenia gravis", "Test_Amount": 6000.0, "Old_Amount": 5000.0, "Spl_Amount": 5000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001297", "Test_Name": "Histoplasma Antibody", "Test_Amount": 5000.0, "Old_Amount": 4500.0, "Spl_Amount": 4500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001303", "Test_Name": "ANTI HCV (ELISA)", "Test_Amount": 1000.0, "Old_Amount": 725.0, "Spl_Amount": 725.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001304", "Test_Name": "Allergy Screening - <PERSON><PERSON><PERSON>", "Test_Amount": 2500.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001325", "Test_Name": "Neuronal Antibody Profile", "Test_Amount": 10000.0, "Old_Amount": 9000.0, "Spl_Amount": 9000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001335", "Test_Name": "Lyme Borrelia <PERSON>dorferi IgM Antibodies", "Test_Amount": 2000.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001350", "Test_Name": "Allergen - Gluten", "Test_Amount": 1600.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001351", "Test_Name": "NMDA Receptor Antibody (NR1)", "Test_Amount": 7000.0, "Old_Amount": 6000.0, "Spl_Amount": 6000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001354", "Test_Name": "VGKC (Lgi1 & CASPR2) Antibody", "Test_Amount": 8500.0, "Old_Amount": 8000.0, "Spl_Amount": 8000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001368", "Test_Name": "Bacterial Meningitis Panel", "Test_Amount": 4500.0, "Old_Amount": 4200.0, "Spl_Amount": 4200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001498", "Test_Name": "ANTI PHOSPHOLIPID ANTIBODY IgA", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001506", "Test_Name": "Beta-2-glycoprotein I-IgA", "Test_Amount": 1800.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001509", "Test_Name": "ENDOMYCIAL ANTIBODY IGG", "Test_Amount": 1800.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001516", "Test_Name": "Helicobacter Pylori -IgA antibodies", "Test_Amount": 2400.0, "Old_Amount": 1900.0, "Spl_Amount": 1900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001517", "Test_Name": "DPT Antibodies (Diphtheria Pertussis Tetanus)-IgG", "Test_Amount": 6500.0, "Old_Amount": 6000.0, "Spl_Amount": 6000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001519", "Test_Name": "Myositis Profile", "Test_Amount": 11000.0, "Old_Amount": 9900.0, "Spl_Amount": 9900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001544", "Test_Name": "ANTI HIV Ag/Ab Combo.", "Test_Amount": 500.0, "Old_Amount": 250.0, "Spl_Amount": 250.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001554", "Test_Name": "<PERSON><PERSON>", "Test_Amount": 1700.0, "Old_Amount": 1600.0, "Spl_Amount": 1600.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001557", "Test_Name": "Tissue Transglutaminase (tTG) Ab-IgG", "Test_Amount": 1500.0, "Old_Amount": 1000.0, "Spl_Amount": 1000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001558", "Test_Name": "Anti dsDNA  IFA", "Test_Amount": 2200.0, "Old_Amount": 1700.0, "Spl_Amount": 1700.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001568", "Test_Name": "SARS-CoV-2 IgG", "Test_Amount": 1000.0, "Old_Amount": 800.0, "Spl_Amount": 800.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001570", "Test_Name": "BK Virus Quantitative  Urine", "Test_Amount": 5000.0, "Old_Amount": 4500.0, "Spl_Amount": 4500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001571", "Test_Name": "Anti-SARS-CoV-2", "Test_Amount": 1000.0, "Old_Amount": 630.0, "Spl_Amount": 630.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001575", "Test_Name": "Clostridium Difficile GDH", "Test_Amount": 2600.0, "Old_Amount": 2200.0, "Spl_Amount": 2200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001576", "Test_Name": "SARS-COV-2 IgM", "Test_Amount": 1000.0, "Old_Amount": 800.0, "Spl_Amount": 800.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001577", "Test_Name": "ECHINOCOCCUS (HYDATID CYST) DETECTION", "Test_Amount": 1200.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001598", "Test_Name": "CHIKUNGUNYA-IGM (ELISA)", "Test_Amount": 1000.0, "Old_Amount": 700.0, "Spl_Amount": 700.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001618", "Test_Name": "Aspergillus fumigatus  Specific IgG", "Test_Amount": 2000.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001627", "Test_Name": "<PERSON><PERSON><PERSON><PERSON> IgG", "Test_Amount": 3500.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001628", "Test_Name": "Legionella Pneumophila antigen", "Test_Amount": 3000.0, "Old_Amount": 2500.0, "Spl_Amount": 2500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001631", "Test_Name": "RNA Polymerase III Antibodies  IgG", "Test_Amount": 4000.0, "Old_Amount": 3300.0, "Spl_Amount": 3300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001649", "Test_Name": "HBsAg- CLIA", "Test_Amount": 600.0, "Old_Amount": 300.0, "Spl_Amount": 300.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001655", "Test_Name": "ALLERGY PROFILE- VEG FOOD ONLY (83 Approx.Test)", "Test_Amount": 2300.0, "Old_Amount": 1900.0, "Spl_Amount": 1900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001656", "Test_Name": "ALLERGY PROFILE- NON-VEG FOOD ONLY (14 Approx. Test)", "Test_Amount": 1500.0, "Old_Amount": 1200.0, "Spl_Amount": 1200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001657", "Test_Name": "ALLERGY PROFILE- INHALANT WITH CONTACTS (100 Approx. Test)", "Test_Amount": 1900.0, "Old_Amount": 1500.0, "Spl_Amount": 1500.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001658", "Test_Name": "ALLERGY PROFILE- DRUG ALLERGY (22 Approx. Test)", "Test_Amount": 2600.0, "Old_Amount": 2000.0, "Spl_Amount": 2000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001659", "Test_Name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS + CONTACTS (183 Approx. Test)", "Test_Amount": 2800.0, "Old_Amount": 2200.0, "Spl_Amount": 2200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001660", "Test_Name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS+ DRUGS (205 Approx. Test)", "Test_Amount": 4800.0, "Old_Amount": 3900.0, "Spl_Amount": 3900.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001661", "Test_Name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS+ NON-VEG (197 Approx. Test)", "Test_Amount": 4100.0, "Old_Amount": 3200.0, "Spl_Amount": 3200.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001662", "Test_Name": "COMPLETE ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS + NON-VEG + DRUGS (222 Approx. Test)", "Test_Amount": 5750.0, "Old_Amount": 5000.0, "Spl_Amount": 5000.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001670", "Test_Name": "HIV I & 2 (CLIA)", "Test_Amount": 1100.0, "Old_Amount": 850.0, "Spl_Amount": 850.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001702", "Test_Name": "Anti HCV IGG", "Test_Amount": 0.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001733", "Test_Name": "SYPHILIS TOTAL ANTIBODY", "Test_Amount": 1350.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "T", "Test_Code": "@001734", "Test_Name": "CHIKUNGUNYA IgG (CARD)", "Test_Amount": 1100.0, "Old_Amount": 0.0, "Spl_Amount": 0.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00014", "Test_Name": "ENA PROFILE", "Test_Amount": 3600.0, "Old_Amount": 2800.0, "Spl_Amount": 2800.0}, {"Dept_Code": "@SE", "Dept_Name": "LAB", "Scheme_Code": "@000002", "Scheme_Name": "L2L", "Test_Type": "P", "Test_Code": "@P00043", "Test_Name": "DENGUE PROFILE", "Test_Amount": 1700.0, "Old_Amount": 1300.0, "Spl_Amount": 1300.0}], "metadata": {"source": "PriceScheme.xls", "total_records": 996, "schemes": ["@000002"], "departments": ["@MS", "@HA", "@BC", "@HP", "@IM", "@HE", "@SE", "@EC", "@MB", "@MO", "@OH", "@RA", "@CP"], "generated_at": "2025-01-01T00:00:00Z"}}